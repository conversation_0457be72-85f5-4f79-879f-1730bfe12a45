package com.telecom.nrm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.telecom.nrm.dao.SplitterDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.service.SplitterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 分光器端口预警业务服务实现类
 * 实现 SplitterService 接口的所有业务逻辑
 * 
 * 主要功能：
 * 1. 实占率和健康状态计算
 * 2. 预测分析算法调用
 * 3. 统计数据处理
 * 4. 数据导出处理
 * 5. 预警处理业务逻辑
 */
@Service
@Slf4j
public class SplitterServiceImpl implements SplitterService {

    @Autowired
    private SplitterDao splitterDao;

    /**
     * 获取统计数据
     * 🎭 替换前端 statistics 演示数据
     */
    @Override
    public JSONObject getStatistics() {
        log.info("开始获取分光器统计数据");
        
        try {
            JSONObject param = new JSONObject();
            // 🔌 后端对接：调用DAO层查询统计数据
            JSONObject statistics = splitterDao.queryStatistics(param, NRMConstants.SHARDING_CODE);
            
            if (statistics == null) {
                // 如果查询失败，返回默认统计数据
                statistics = new JSONObject();
                statistics.put("normalDevices", 0);
                statistics.put("attentionDevices", 0);
                statistics.put("alarmDevices", 0);
                statistics.put("totalDevices", 0);
                statistics.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            
            log.info("获取统计数据成功: {}", statistics);
            return statistics;
            
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            // 返回默认统计数据
            JSONObject defaultStats = new JSONObject();
            defaultStats.put("normalDevices", 0);
            defaultStats.put("attentionDevices", 0);
            defaultStats.put("alarmDevices", 0);
            defaultStats.put("totalDevices", 0);
            defaultStats.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            return defaultStats;
        }
    }

    /**
     * 计算设备实占率
     * 📊 对应前端 calculateOccupancyRate 计算逻辑
     */
    @Override
    public double calculateOccupancyRate(JSONObject device) {
        try {
            // 获取分光器容量和空闲数
            Integer capacity = device.getInteger("分光器容量");
            Integer freeCount = device.getInteger("分光器空闲数");
            
            // 容量为空或为0时，实占率为0
            if (capacity == null || capacity == 0) {
                return 0.0;
            }
            
            // 空闲数为空时，默认为0
            if (freeCount == null) {
                freeCount = 0;
            }
            
            // 计算实占率：(容量 - 空闲数) / 容量 * 100
            double occupancyRate = (1.0 - (double) freeCount / capacity) * 100;
            
            // 确保实占率在0-100之间
            occupancyRate = Math.max(0, Math.min(100, occupancyRate));
            
            // 保留两位小数
            return Math.round(occupancyRate * 100.0) / 100.0;
            
        } catch (Exception e) {
            log.error("计算实占率失败，设备数据: {}", device, e);
            return 0.0;
        }
    }

    /**
     * 计算设备健康状态
     * 📊 对应前端 calculateHealthStatus 计算逻辑
     */
    @Override
    public String calculateHealthStatus(double occupancyRate) {
        // 根据实占率判断健康状态
        if (occupancyRate >= 90) {
            return "alarm";      // 告警：实占率 >= 90%
        } else if (occupancyRate >= 80) {
            return "attention";  // 注意：实占率 >= 80%
        } else {
            return "normal";     // 正常：实占率 < 80%
        }
    }

    /**
     * 获取设备分光器详情
     * 🎭 替换前端 splitterDetails 演示数据
     */
    @Override
    public List<JSONObject> getSplitterDetails(String deviceCode) {
        log.info("开始获取设备分光器详情，设备编码: {}", deviceCode);
        
        try {
            JSONObject param = new JSONObject();
            param.put("deviceCode", deviceCode);
            
            // 🔌 后端对接：调用DAO层查询分光器详情
            List<JSONObject> splitterDetails = splitterDao.querySplitterDetails(param, NRMConstants.SHARDING_CODE);
            
            if (splitterDetails == null) {
                splitterDetails = new ArrayList<>();
            }
            
            // 📊 为每个分光器计算实占率和健康状态
            for (JSONObject splitter : splitterDetails) {
                double occupancyRate = calculateOccupancyRate(splitter);
                splitter.put("实占率", occupancyRate);
                
                String healthStatus = calculateHealthStatus(occupancyRate);
                splitter.put("健康状态", healthStatus);
            }
            
            log.info("获取设备分光器详情成功，设备编码: {}, 分光器数量: {}", deviceCode, splitterDetails.size());
            return splitterDetails;
            
        } catch (Exception e) {
            log.error("获取设备分光器详情失败，设备编码: {}", deviceCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 单设备预测分析
     * 🎭 替换前端 predictionData 演示数据
     */
    @Override
    public JSONObject predictSingleDevice(JSONObject request) {
        String deviceCode = request.getString("deviceCode");
        String period = request.getString("period");
        
        log.info("开始单设备预测分析，设备编码: {}, 预测周期: {}", deviceCode, period);
        
        try {
            // 🔌 后端对接：获取历史数据
            List<JSONObject> historicalData = getHistoricalData(deviceCode, "recent");
            
            if (historicalData == null || historicalData.isEmpty()) {
                // 如果没有历史数据，返回无数据状态
                JSONObject result = new JSONObject();
                result.put("hasData", false);
                result.put("message", "暂无历史数据，无法进行预测分析");
                return result;
            }
            
            // 🔌 后端对接：执行预测算法
            JSONObject prediction = executePredictionAlgorithm(historicalData, period);
            
            // 📊 生成建议方案
            JSONObject recommendations = generateRecommendations(prediction);
            
            // 组装预测结果
            JSONObject result = new JSONObject();
            result.put("hasData", true);
            result.put("设备编码", deviceCode);
            result.put("historicalData", historicalData);
            result.put("prediction", prediction);
            result.put("recommendations", recommendations);
            
            // 🔌 后端对接：保存预测结果到数据库
            updatePredictionResult(deviceCode, result);
            
            log.info("单设备预测分析完成，设备编码: {}", deviceCode);
            return result;
            
        } catch (Exception e) {
            log.error("单设备预测分析失败，设备编码: {}", deviceCode, e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("hasData", false);
            errorResult.put("error", "预测分析失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取设备预测详情
     * 🎭 替换前端 devicePredictionData 演示数据
     */
    @Override
    public JSONObject getDevicePredictionDetail(String deviceCode) {
        log.info("开始获取设备预测详情，设备编码: {}", deviceCode);
        
        try {
            // 先执行基础预测分析
            JSONObject basicRequest = new JSONObject();
            basicRequest.put("deviceCode", deviceCode);
            basicRequest.put("period", "month");
            
            JSONObject basicPrediction = predictSingleDevice(basicRequest);
            
            if (!basicPrediction.getBooleanValue("hasData")) {
                return basicPrediction;
            }
            
            // 📊 添加风险评估
            JSONObject riskAssessment = calculateRiskAssessment(deviceCode);
            basicPrediction.put("riskAssessment", riskAssessment);
            
            // 📊 添加业务洞察
            JSONObject businessInsights = generateBusinessInsights(deviceCode);
            basicPrediction.put("businessInsights", businessInsights);
            
            log.info("获取设备预测详情完成，设备编码: {}", deviceCode);
            return basicPrediction;
            
        } catch (Exception e) {
            log.error("获取设备预测详情失败，设备编码: {}", deviceCode, e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("hasData", false);
            errorResult.put("error", "获取预测详情失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 批量预测所有设备
     * 🎭 替换前端批量预测功能
     */
    @Override
    public List<JSONObject> predictAllDevices() {
        log.info("开始批量预测所有设备");
        
        try {
            JSONObject param = new JSONObject();
            // 🔌 后端对接：查询所有设备
            List<JSONObject> allDevices = splitterDao.queryAllDevices(param, NRMConstants.SHARDING_CODE);
            
            List<JSONObject> predictions = new ArrayList<>();
            
            if (allDevices != null && !allDevices.isEmpty()) {
                for (JSONObject device : allDevices) {
                    String deviceCode = device.getString("设备编码");
                    
                    try {
                        // 对每个设备执行预测
                        JSONObject request = new JSONObject();
                        request.put("deviceCode", deviceCode);
                        request.put("period", "week");
                        
                        JSONObject prediction = predictSingleDevice(request);
                        
                        if (prediction.getBooleanValue("hasData")) {
                            JSONObject predictionResult = prediction.getJSONObject("prediction");
                            
                            JSONObject result = new JSONObject();
                            result.put("设备编码", deviceCode);
                            result.put("预测实占率", predictionResult.getDouble("nextWeekRate"));
                            result.put("预测空闲数", calculatePredictedFreeCount(device, predictionResult));
                            result.put("预测状态", calculatePredictedStatus(predictionResult));
                            
                            predictions.add(result);
                        }
                        
                    } catch (Exception e) {
                        log.error("设备预测失败，设备编码: {}", deviceCode, e);
                    }
                }
                
                // 🔌 后端对接：批量更新预测结果
                if (!predictions.isEmpty()) {
                    JSONObject batchParam = new JSONObject();
                    batchParam.put("predictions", predictions);
                    splitterDao.batchUpdatePredictionResult(batchParam, NRMConstants.SHARDING_CODE);
                }
            }
            
            log.info("批量预测完成，成功预测设备数量: {}", predictions.size());
            return predictions;
            
        } catch (Exception e) {
            log.error("批量预测失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取趋势数据
     * 🎭 替换前端 trendData 演示数据
     */
    @Override
    public List<JSONObject> getTrendData(JSONObject params) {
        String deviceCode = params.getString("deviceCode");
        String timeRange = params.getString("timeRange");

        log.info("开始获取趋势数据，设备编码: {}, 时间范围: {}", deviceCode, timeRange);

        try {
            // 🔌 后端对接：调用DAO层查询趋势数据
            List<JSONObject> trendData = splitterDao.queryTrendData(params, NRMConstants.SHARDING_CODE);

            if (trendData == null) {
                trendData = new ArrayList<>();
            }

            log.info("获取趋势数据成功，设备编码: {}, 数据点数量: {}", deviceCode, trendData.size());
            return trendData;

        } catch (Exception e) {
            log.error("获取趋势数据失败，设备编码: {}", deviceCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 数据导出
     * 🎭 替换前端导出功能
     */
    @Override
    public void exportData(JSONObject params, HttpServletResponse response) {
        log.info("开始数据导出，参数: {}", params);

        try {
            // 🔌 后端对接：查询导出数据
            List<JSONObject> exportData = splitterDao.queryExportData(params, NRMConstants.SHARDING_CODE);

            if (exportData == null) {
                exportData = new ArrayList<>();
            }

            // 🔌 后端对接：生成Excel文件
            byte[] excelBytes = generateExcelFile(exportData);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=splitter_data_export.xlsx");
            response.setContentLength(excelBytes.length);

            // 写入响应流
            response.getOutputStream().write(excelBytes);
            response.getOutputStream().flush();

            log.info("数据导出成功，导出记录数: {}", exportData.size());

        } catch (IOException e) {
            log.error("数据导出失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 处理设备预警
     * 🎭 替换前端预警处理功能
     */
    @Override
    public JSONObject handleDeviceWarning(String deviceCode, JSONObject request) {
        log.info("开始处理设备预警，设备编码: {}, 请求参数: {}", deviceCode, request);

        try {
            // 设置处理参数
            JSONObject updateParam = new JSONObject();
            updateParam.put("deviceCode", deviceCode);
            updateParam.put("handlerName", request.getString("handlerName"));
            updateParam.put("handleNote", request.getString("handleNote"));
            updateParam.put("handleTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            updateParam.put("handleStatus", "已处理");

            // 🔌 后端对接：更新预警处理状态
            Integer updateCount = splitterDao.updateWarningStatus(updateParam, NRMConstants.SHARDING_CODE);

            JSONObject result = new JSONObject();
            if (updateCount != null && updateCount > 0) {
                result.put("success", true);
                result.put("message", "预警处理成功");

                // 🔌 后端对接：记录操作日志
                JSONObject logParam = new JSONObject();
                logParam.put("deviceCode", deviceCode);
                logParam.put("operation", "预警处理");
                logParam.put("operator", request.getString("handlerName"));
                logParam.put("operationTime", updateParam.getString("handleTime"));
                splitterDao.insertOperationLog(logParam, NRMConstants.SHARDING_CODE);

            } else {
                result.put("success", false);
                result.put("message", "预警处理失败");
            }

            log.info("设备预警处理完成，设备编码: {}, 结果: {}", deviceCode, result.getBooleanValue("success"));
            return result;

        } catch (Exception e) {
            log.error("处理设备预警失败，设备编码: {}", deviceCode, e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("message", "预警处理失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 执行预测算法
     * 🔌 后端对接：需要集成预测算法服务
     */
    @Override
    public JSONObject executePredictionAlgorithm(List<JSONObject> historicalData, String period) {
        log.info("开始执行预测算法，历史数据点数: {}, 预测周期: {}", historicalData.size(), period);

        try {
            // 🔌 后端对接：这里需要调用实际的预测算法
            // 可以是机器学习算法、统计算法或调用外部预测服务
            // 目前使用简单的线性趋势预测作为示例

            if (historicalData.size() < 2) {
                throw new RuntimeException("历史数据不足，无法进行预测");
            }

            // 提取实占率数据
            List<Double> rates = new ArrayList<>();
            for (JSONObject data : historicalData) {
                Double rate = data.getDouble("occupancyRate");
                if (rate != null) {
                    rates.add(rate);
                }
            }

            if (rates.size() < 2) {
                throw new RuntimeException("有效历史数据不足，无法进行预测");
            }

            // 简单线性趋势计算
            double sum = 0;
            for (int i = 1; i < rates.size(); i++) {
                sum += rates.get(i) - rates.get(i - 1);
            }
            double avgTrend = sum / (rates.size() - 1);

            // 计算预测值
            double currentRate = rates.get(rates.size() - 1);
            double nextWeekRate = currentRate + avgTrend;
            double nextMonthRate = currentRate + avgTrend * 4; // 假设一个月4周

            // 确保预测值在合理范围内
            nextWeekRate = Math.max(0, Math.min(100, nextWeekRate));
            nextMonthRate = Math.max(0, Math.min(100, nextMonthRate));

            // 判断趋势
            String trend;
            if (avgTrend > 2) {
                trend = "increasing";
            } else if (avgTrend < -2) {
                trend = "decreasing";
            } else {
                trend = "stable";
            }

            JSONObject prediction = new JSONObject();
            prediction.put("nextWeekRate", Math.round(nextWeekRate * 100.0) / 100.0);
            prediction.put("nextMonthRate", Math.round(nextMonthRate * 100.0) / 100.0);
            prediction.put("trend", trend);
            prediction.put("confidence", 85.0); // 置信度

            log.info("预测算法执行完成，预测结果: {}", prediction);
            return prediction;

        } catch (Exception e) {
            log.error("执行预测算法失败", e);
            throw new RuntimeException("预测算法执行失败: " + e.getMessage());
        }
    }

    /**
     * 生成建议方案
     * 📊 根据预测结果生成扩容和调整建议
     */
    @Override
    public JSONObject generateRecommendations(JSONObject predictionResult) {
        try {
            Double nextMonthRate = predictionResult.getDouble("nextMonthRate");
            String trend = predictionResult.getString("trend");

            JSONObject recommendations = new JSONObject();

            if (nextMonthRate >= 95) {
                recommendations.put("expansionAdvice", "紧急扩容：建议立即增加分光器容量");
                recommendations.put("adjustmentAdvice", "立即进行负载均衡调整");
                recommendations.put("priority", "high");
            } else if (nextMonthRate >= 85) {
                recommendations.put("expansionAdvice", "建议在2周内增加分光器容量");
                recommendations.put("adjustmentAdvice", "可考虑将部分用户迁移至邻近设备");
                recommendations.put("priority", "medium");
            } else if ("increasing".equals(trend)) {
                recommendations.put("expansionAdvice", "建议关注设备使用情况，适时扩容");
                recommendations.put("adjustmentAdvice", "定期监控设备负载情况");
                recommendations.put("priority", "low");
            } else {
                recommendations.put("expansionAdvice", "当前容量充足，无需扩容");
                recommendations.put("adjustmentAdvice", "保持现有配置");
                recommendations.put("priority", "low");
            }

            return recommendations;

        } catch (Exception e) {
            log.error("生成建议方案失败", e);
            JSONObject defaultRecommendations = new JSONObject();
            defaultRecommendations.put("expansionAdvice", "请联系技术人员进行评估");
            defaultRecommendations.put("adjustmentAdvice", "请联系技术人员进行评估");
            defaultRecommendations.put("priority", "medium");
            return defaultRecommendations;
        }
    }

    /**
     * 计算风险评估
     * 📊 根据设备状态和预测结果计算风险等级
     */
    @Override
    public JSONObject calculateRiskAssessment(String deviceCode) {
        try {
            // 🔌 后端对接：查询设备详细信息
            JSONObject param = new JSONObject();
            param.put("deviceCode", deviceCode);
            JSONObject deviceDetail = splitterDao.queryDeviceDetail(param, NRMConstants.SHARDING_CODE);

            JSONObject riskAssessment = new JSONObject();

            if (deviceDetail != null) {
                double currentRate = calculateOccupancyRate(deviceDetail);
                List<String> reasons = new ArrayList<>();
                int score = 0;

                // 根据当前实占率评分
                if (currentRate >= 90) {
                    score += 40;
                    reasons.add("当前实占率过高");
                } else if (currentRate >= 80) {
                    score += 25;
                    reasons.add("当前实占率较高");
                }

                // 根据趋势评分
                List<JSONObject> historicalData = getHistoricalData(deviceCode, "recent");
                if (historicalData != null && historicalData.size() >= 3) {
                    // 计算趋势
                    double firstRate = historicalData.get(0).getDouble("occupancyRate");
                    double lastRate = historicalData.get(historicalData.size() - 1).getDouble("occupancyRate");
                    double trendRate = lastRate - firstRate;

                    if (trendRate > 10) {
                        score += 30;
                        reasons.add("实占率上升趋势明显");
                    } else if (trendRate > 5) {
                        score += 15;
                        reasons.add("实占率呈上升趋势");
                    }
                }

                // 根据设备重要性评分（可以根据实际业务调整）
                Integer coveredRooms = deviceDetail.getInteger("覆盖的工程级的线路到达房间数");
                if (coveredRooms != null && coveredRooms > 500) {
                    score += 15;
                    reasons.add("设备覆盖范围较大");
                }

                // 确定风险等级
                String level;
                if (score >= 70) {
                    level = "high";
                } else if (score >= 40) {
                    level = "medium";
                } else {
                    level = "low";
                }

                riskAssessment.put("score", score);
                riskAssessment.put("level", level);
                riskAssessment.put("reasons", reasons);
            } else {
                riskAssessment.put("score", 0);
                riskAssessment.put("level", "low");
                riskAssessment.put("reasons", new ArrayList<>());
            }

            return riskAssessment;

        } catch (Exception e) {
            log.error("计算风险评估失败，设备编码: {}", deviceCode, e);
            JSONObject defaultRisk = new JSONObject();
            defaultRisk.put("score", 50);
            defaultRisk.put("level", "medium");
            defaultRisk.put("reasons", List.of("无法获取详细信息"));
            return defaultRisk;
        }
    }

    /**
     * 生成业务洞察
     * 📊 基于数据分析生成业务洞察信息
     */
    @Override
    public JSONObject generateBusinessInsights(String deviceCode) {
        try {
            JSONObject insights = new JSONObject();

            // 🔌 后端对接：获取历史数据进行分析
            List<JSONObject> historicalData = getHistoricalData(deviceCode, "extended");

            if (historicalData != null && !historicalData.isEmpty()) {
                // 分析使用率变化趋势
                double avgRate = historicalData.stream()
                    .mapToDouble(data -> data.getDouble("occupancyRate"))
                    .average()
                    .orElse(0.0);

                String trendAnalysis;
                if (avgRate >= 85) {
                    trendAnalysis = "该设备使用率持续处于高位，需要重点关注";
                } else if (avgRate >= 70) {
                    trendAnalysis = "该设备使用率处于中等水平，建议定期监控";
                } else {
                    trendAnalysis = "该设备使用率较低，资源利用充分";
                }

                insights.put("trendAnalysis", trendAnalysis);
                insights.put("expansionAdvice", avgRate >= 80 ? "建议制定扩容计划" : "暂无扩容需求");
                insights.put("confidenceLevel", 92);
            } else {
                insights.put("trendAnalysis", "暂无足够历史数据进行分析");
                insights.put("expansionAdvice", "建议收集更多数据后再进行评估");
                insights.put("confidenceLevel", 60);
            }

            return insights;

        } catch (Exception e) {
            log.error("生成业务洞察失败，设备编码: {}", deviceCode, e);
            JSONObject defaultInsights = new JSONObject();
            defaultInsights.put("trendAnalysis", "数据分析异常，请联系技术人员");
            defaultInsights.put("expansionAdvice", "请联系技术人员进行评估");
            defaultInsights.put("confidenceLevel", 50);
            return defaultInsights;
        }
    }

    /**
     * 计算预测空闲数
     * 📊 根据预测实占率计算预测空闲端口数
     */
    @Override
    public int calculatePredictedFreeCount(JSONObject device, JSONObject prediction) {
        try {
            Integer capacity = device.getInteger("分光器容量");
            Double predictedRate = prediction.getDouble("nextWeekRate");

            if (capacity == null || predictedRate == null) {
                return 0;
            }

            // 计算预测空闲数：容量 * (1 - 预测实占率/100)
            int predictedFreeCount = (int) Math.round(capacity * (1 - predictedRate / 100));
            return Math.max(0, predictedFreeCount);

        } catch (Exception e) {
            log.error("计算预测空闲数失败", e);
            return 0;
        }
    }

    /**
     * 计算预测状态
     * 📊 根据预测结果计算预测健康状态
     */
    @Override
    public String calculatePredictedStatus(JSONObject prediction) {
        try {
            Double predictedRate = prediction.getDouble("nextWeekRate");
            if (predictedRate == null) {
                return "normal";
            }

            // 使用与当前健康状态相同的判断逻辑
            return calculateHealthStatus(predictedRate);

        } catch (Exception e) {
            log.error("计算预测状态失败", e);
            return "normal";
        }
    }

    /**
     * 生成Excel文件
     * 🔌 后端对接：需要Excel生成工具库支持
     */
    @Override
    public byte[] generateExcelFile(List<JSONObject> data) {
        try {
            // 🔌 后端对接：这里需要使用Excel生成工具库
            // 例如Apache POI、EasyExcel等
            // 目前返回示例字节数组

            log.info("开始生成Excel文件，数据行数: {}", data.size());

            // 示例：创建简单的CSV格式数据
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("设备编码,区域,实占率,健康状态,分光器容量,分光器空闲数\n");

            for (JSONObject item : data) {
                csvContent.append(item.getString("设备编码")).append(",");
                csvContent.append(item.getString("区域")).append(",");
                csvContent.append(item.getDouble("实占率")).append("%,");
                csvContent.append(item.getString("健康状态")).append(",");
                csvContent.append(item.getInteger("分光器容量")).append(",");
                csvContent.append(item.getInteger("分光器空闲数")).append("\n");
            }

            return csvContent.toString().getBytes("UTF-8");

        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            return "导出失败".getBytes();
        }
    }

    /**
     * 更新预测结果到数据库
     * 🔌 后端对接：将预测结果保存到数据库
     */
    @Override
    public int updatePredictionResult(String deviceCode, JSONObject predictionResult) {
        try {
            JSONObject prediction = predictionResult.getJSONObject("prediction");
            if (prediction == null) {
                return 0;
            }

            JSONObject updateParam = new JSONObject();
            updateParam.put("deviceCode", deviceCode);
            updateParam.put("predictedRate", prediction.getDouble("nextWeekRate"));
            updateParam.put("predictedTrend", prediction.getString("trend"));
            updateParam.put("confidence", prediction.getDouble("confidence"));
            updateParam.put("updateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            Integer result = splitterDao.updatePredictionResult(updateParam, NRMConstants.SHARDING_CODE);
            return result != null ? result : 0;

        } catch (Exception e) {
            log.error("更新预测结果失败，设备编码: {}", deviceCode, e);
            return 0;
        }
    }

    /**
     * 获取历史数据
     * 🔌 后端对接：从数据库查询设备历史使用率数据
     */
    @Override
    public List<JSONObject> getHistoricalData(String deviceCode, String timeRange) {
        try {
            JSONObject param = new JSONObject();
            param.put("deviceCode", deviceCode);
            param.put("timeRange", timeRange);

            List<JSONObject> historicalData = splitterDao.queryHistoricalData(param, NRMConstants.SHARDING_CODE);
            return historicalData != null ? historicalData : new ArrayList<>();

        } catch (Exception e) {
            log.error("获取历史数据失败，设备编码: {}", deviceCode, e);
            return new ArrayList<>();
        }
    }

    /**
     * 统计字典数据
     * 🎭 替换前端统计相关的字典数据
     */
    @Override
    public JSONObject getStatisticsDictionary(JSONObject params) {
        try {
            JSONObject dictionary = new JSONObject();

            // 获取统计数据
            dictionary.put("statistics", getStatistics());

            // 获取区域列表
            JSONObject regionParam = new JSONObject();
            List<JSONObject> regions = splitterDao.queryRegions(regionParam, NRMConstants.SHARDING_CODE);
            dictionary.put("regions", regions);

            // 获取状态列表
            List<JSONObject> statusList = new ArrayList<>();
            statusList.add(createStatusItem("正常", "normal"));
            statusList.add(createStatusItem("注意", "attention"));
            statusList.add(createStatusItem("告警", "alarm"));
            dictionary.put("statusList", statusList);

            return dictionary;

        } catch (Exception e) {
            log.error("获取统计字典数据失败", e);
            return new JSONObject();
        }
    }

    /**
     * 创建状态项
     * 辅助方法，用于创建状态字典项
     */
    private JSONObject createStatusItem(String name, String value) {
        JSONObject status = new JSONObject();
        status.put("statusName", name);
        status.put("statusValue", value);
        return status;
    }
}
