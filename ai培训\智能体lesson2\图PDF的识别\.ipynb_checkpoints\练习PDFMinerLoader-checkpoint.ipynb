{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6a5cba88", "metadata": {"ExecuteTime": {"end_time": "2025-04-23T22:53:40.975083Z", "start_time": "2025-04-23T22:53:40.242132Z"}}, "outputs": [], "source": ["#单纯的文本加载\n", "from langchain_community.document_loaders import PDFMinerLoader\n", "\n", "file_path = \"图像文稿.pdf\"\n", "loader = PDFMinerLoader(file_path)"]}, {"cell_type": "code", "execution_count": 2, "id": "70e8c446", "metadata": {"ExecuteTime": {"end_time": "2025-04-23T22:54:11.010923Z", "start_time": "2025-04-23T22:54:00.689916Z"}}, "outputs": [{"data": {"text/plain": ["Document(metadata={'producer': '', 'creator': 'WPS 文字', 'creationdate': '2025-04-25T17:19:38+08:00', 'author': '张浩~大数据~建模_AI_挖掘', 'comments': '', 'company': '', 'keywords': '', 'moddate': '2025-04-25T17:19:38+08:00', 'sourcemodified': \"D:20250425171938+08'00'\", 'subject': '', 'title': '', 'trapped': 'False', 'total_pages': 2, 'source': '图像文稿.pdf'}, page_content='\\n\\x0c')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = loader.load()\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 3, "id": "c699426b", "metadata": {"ExecuteTime": {"end_time": "2025-04-23T23:04:17.348595Z", "start_time": "2025-04-23T23:03:33.315910Z"}}, "outputs": [], "source": ["#使用RapidOCRBlobParser实现图像识别技术\n", "# pip install rapidocr-onnxruntime\n", "# pip install pdfminer.six\n", "from langchain_community.document_loaders.parsers import RapidOCRBlobParser\n", "from langchain_community.document_loaders import PDFMinerLoader\n", "loader = PDFMinerLoader(\n", "    \"图像文稿.pdf\",\n", "    mode=\"page\",\n", "    images_inner_format=\"markdown-img\",\n", "    images_parser=RapidOCRBlobParser(),\n", ")\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": 4, "id": "c66a34a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["![实验：内容审核工作流的搭建\n", "（100分）\n", "文件数据：1班第二天考核数据内容审核数据.cSV\n", "1.使用langchain链接到deepseek（10分）\n", "2.创建内容审核prompt 模版(20分)\n", "3.创建chain  (30分)\n", "4.循环把所有用户信息都做内容审核，输出字段：用户内容、合规/不合规（30）\n", "（代码+数据）\n", "5.把所有内容做成向量（10分）（只要代码体现即可）\n", "<EMAIL>](#)\n", "-------------\n", "![模型格式分类与对比\n", "格式类型\n", "核心特点\n", "适用场景\n", "显存占用\n", "推理速度\n", "兼容性\n", "原始模型文件\n", "微调训练、全精度推\n", "完整权重\n", "高\n", "中等\n", "PyTorch/TensorFlow\n", "(如.bin/.safetensors)\n", "理\n", "低\n", "4/8bit量\n", "GPTQ/AWQ/QLORA等\n", "低显存GPU部署、快\n", "Hugging Face\n", "(降低30-\n", "快\n", "化\n", "压缩技术\n", "速推理\n", "Transformers\n", "70%)\n", "llama.cpp优化的单文件\n", "llama.cpp、LM\n", "GGUF\n", "边缘设备、CPU推理\n", "极低\n", "中等\n", "格式\n", "Studio\n", "ONNX\n", "多硬件适配、生产环\n", "ONNX\n", "跨平台标准化格式\n", "中等\n", "快\n", "Runtime\n", "境部署\n", "Runtime/TensorRT\n", "NVIDIAGPU深度优化\n", "极致性能、工业级推\n", "TensorRT\n", "中等\n", "极快\n", "NVIDIA GPU\n", "引擎\n", "理](#)\n", "-------------\n"]}], "source": ["for x in docs:\n", "    print(x.page_content)\n", "    print('-------------')"]}, {"cell_type": "code", "execution_count": null, "id": "0365c1f9-58c3-42d5-bd4c-c2cf1c6b1f75", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}