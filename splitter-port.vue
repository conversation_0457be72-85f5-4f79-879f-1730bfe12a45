<template>
  <PageWrapper>
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button type="text" @click="goBack" class="back-btn">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
        <h1 class="page-title">分光器端口预警</h1>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="refreshData" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card normal">
            <a-statistic
              title="正常设备"
              :value="statistics.normalDevices"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card attention">
            <a-statistic
              title="注意设备"
              :value="statistics.attentionDevices"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card alarm">
            <a-statistic
              title="告警设备"
              :value="statistics.alarmDevices"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <CloseCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card total">
            <a-statistic
              title="总设备数"
              :value="statistics.totalDevices"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <DatabaseOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
      <div class="update-time">
        <ClockCircleOutlined />
        最近更新时间：{{ statistics.lastUpdateTime }}
      </div>
    </div>

    <!-- 趋势图可视化区域 -->
    <a-card class="trend-chart-card">
      <template #title>
        <div class="trend-chart-header">
          <span>实占率趋势图</span>
          <a-button
            type="text"
            size="small"
            @click="toggleTrendChart"
            class="collapse-btn"
          >
            <template #icon>
              <UpOutlined v-if="trendChartCollapsed" />
              <DownOutlined v-else />
            </template>
            {{ trendChartCollapsed ? '展开' : '收起' }}
          </a-button>
        </div>
      </template>

      <div v-show="!trendChartCollapsed" class="trend-chart-content">
        <!-- 趋势图配置区域 -->
        <div class="trend-config">
          <a-form layout="inline" :model="trendConfig">
            <a-form-item label="设备/分光器编码">
              <a-input-search
                v-model:value="trendConfig.searchCode"
                placeholder="请输入设备编码或分光器编码"
                style="width: 300px"
                enter-button="搜索"
                @search="onSearchTrend"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="时间范围">
              <a-radio-group v-model:value="trendConfig.timeRange" @change="onTimeRangeChange">
                <a-radio-button value="month">最近30天</a-radio-button>
                <a-radio-button value="quarter">最近90天</a-radio-button>
              </a-radio-group>
            </a-form-item>
            <a-form-item>
              <a-button @click="clearTrendSelection" size="small">
                清空
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <!-- 图表展示区域 -->
        <div class="chart-display">
          <div v-if="trendConfig.currentDevice" class="chart-container">
            <div class="chart-header">
              <span class="device-info">{{ trendConfig.currentDevice }} 实占率趋势</span>
            </div>
            <div ref="trendChartRef" style="width: 100%; height: 350px;"></div>
          </div>
          <div v-else class="empty-chart">
            <a-empty description="请输入设备编码或分光器编码查看趋势图" />
          </div>
        </div>
      </div>
    </a-card>

    <!-- 搜索筛选区域 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filters" @finish="handleSearch">
        <a-form-item label="设备编码">
          <a-input
            v-model:value="filters.deviceCode"
            placeholder="请输入设备编码"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="区域">
          <a-select
            v-model:value="filters.region"
            placeholder="请选择区域"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="常州">常州</a-select-option>
            <a-select-option value="无锡">无锡</a-select-option>
            <a-select-option value="苏州">苏州</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备状态">
          <a-select
            v-model:value="filters.deviceStatus"
            placeholder="请选择设备状态"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="normal">正常</a-select-option>
            <a-select-option value="attention">注意</a-select-option>
            <a-select-option value="alarm">告警</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="实占率">
          <a-select
            v-model:value="filters.occupancyRate"
            placeholder="请选择实占率范围"
            style="width: 150px"
            allow-clear
          >
            <a-select-option v-for="option in occupancyRateOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="空闲端口数">
          <a-select
            v-model:value="filters.freePortCount"
            placeholder="请选择端口数"
            style="width: 150px"
            allow-clear
          >
            <a-select-option v-for="option in freePortOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="入网时间">
          <a-date-picker
            v-model:value="filters.installAfterTime"
            picker="month"
            placeholder="选择年月后入网"
            style="width: 180px"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
            <a-button @click="handleReset">重置</a-button>
            <a-button type="primary" @click="executeAllPrediction" :loading="allPredictionLoading">
              <template #icon>
                <BarChartOutlined />
              </template>
              全部预测
            </a-button>
            <a-button @click="handleExport">
              <template #icon>
                <ExportOutlined />
              </template>
              导出数据
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 设备容器数据表格 -->
    <a-card class="table-card">
      <a-table
        :columns="columns"
        :data-source="deviceList"
        :pagination="pagination"
        :loading="loading"
        row-key="设备编码"
        :scroll="{ x: 1800, y: 500 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === '实占率'">
            <div class="occupancy-cell">
              <a-progress
                :percent="record.实占率"
                :stroke-color="getProgressColor(record.实占率)"
                :show-info="false"
                size="small"
              />
              <span class="occupancy-text">{{ record.实占率 }}%</span>
            </div>
          </template>
          <template v-if="column.key === '健康状态'">
            <a-tag :color="getStatusColor(record.健康状态)">
              {{ getStatusText(record.健康状态) }}
            </a-tag>
          </template>
          <template v-if="column.key === '预测实占率'">
            <div v-if="record.预测实占率 !== undefined" class="occupancy-cell">
              <a-progress
                :percent="record.预测实占率"
                :stroke-color="getProgressColor(record.预测实占率)"
                :show-info="false"
                size="small"
              />
              <span class="occupancy-text">{{ record.预测实占率 }}%</span>
            </div>
            <span v-else class="no-prediction">未预测</span>
          </template>
          <template v-if="column.key === '预测空闲数'">
            <span v-if="record.预测空闲数 !== undefined">
              {{ record.预测空闲数 }}
            </span>
            <span v-else class="no-prediction">-</span>
          </template>
          <template v-if="column.key === '预测状态'">
            <a-tag v-if="record.预测状态" :color="getPredictionStatusColor(record.预测状态)">
              {{ getPredictionStatusText(record.预测状态) }}
            </a-tag>
            <span v-else class="no-prediction">-</span>
          </template>
          <template v-if="column.key === 'action'">
            <div class="action-buttons">
              <a-button type="link" size="small" @click="showDeviceDetail(record)" class="action-btn">
                详情
              </a-button>
              <a-button type="link" size="small" @click="showDevicePrediction(record)" class="action-btn">
                查看预测
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 分光器详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="分光器详情"
      width="1200px"
      :footer="null"
    >
      <a-table
        :columns="detailColumns"
        :data-source="splitterDetails"
        :pagination="false"
        size="small"
        row-key="splitterCode"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === '实占率'">
            <div class="occupancy-cell">
              <a-progress
                :percent="record.实占率"
                :stroke-color="getProgressColor(record.实占率)"
                :show-info="false"
                size="small"
              />
              <span class="occupancy-text">{{ record.实占率 }}%</span>
            </div>
          </template>
          <template v-if="column.key === '健康状态'">
            <a-tag :color="getStatusColor(record.健康状态)">
              {{ getStatusText(record.健康状态) }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>



    <!-- 设备预测详情弹窗 -->
    <a-modal
      v-model:open="devicePredictionModalVisible"
      title="设备预测详情"
      width="1000px"
      :footer="null"
      @cancel="handleDevicePredictionModalClose"
    >
      <div class="device-prediction-content">
        <!-- 设备基本信息 -->
        <div class="device-info-header">
          <a-row justify="space-between" align="middle">
            <a-col :span="20">
              <a-descriptions :column="3" size="small">
                <a-descriptions-item label="设备编码">{{ selectedDeviceForPrediction?.设备编码 }}</a-descriptions-item>
                <a-descriptions-item label="区域">{{ selectedDeviceForPrediction?.区域 }}</a-descriptions-item>
                <a-descriptions-item label="当前实占率">{{ selectedDeviceForPrediction?.实占率 }}%</a-descriptions-item>
              </a-descriptions>
            </a-col>
            <a-col :span="4" style="text-align: right;">
              <a-button
                type="primary"
                size="small"
                :loading="refreshPredictionLoading"
                @click="refreshDevicePrediction"
              >
                🔄 重新预测
              </a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 趋势图表 -->
        <div class="prediction-chart-container">
          <div v-if="devicePredictionData.hasData" ref="devicePredictionChartRef" style="width: 100%; height: 350px;"></div>
          <div v-else class="chart-loading">
            <a-spin size="large" />
            <p style="margin-top: 16px; text-align: center;">正在加载图表数据...</p>
          </div>
        </div>

        <!-- 预测结果详情（新的两大模块布局） -->
        <div class="device-prediction-info" v-if="devicePredictionData.hasData">
          <a-row :gutter="16">
            <!-- 预测结果模块 -->
            <a-col :span="12">
              <a-card title="📊 预测结果" size="small">
                <div class="prediction-results">
                  <p><strong>下周预测实占率：</strong><span class="highlight">{{ devicePredictionData.predictionResults?.nextWeekRate }}%</span></p>
                  <p><strong>下月预测实占率：</strong><span class="highlight">{{ devicePredictionData.predictionResults?.nextMonthRate }}%</span></p>
                  <p><strong>趋势：</strong><a-tag :color="getTrendColor(devicePredictionData.predictionResults?.trend)">{{ devicePredictionData.predictionResults?.trend }}</a-tag></p>
                </div>
              </a-card>
            </a-col>

            <!-- 风险评估与建议模块 -->
            <a-col :span="12">
              <a-card size="small">
                <template #title>
                  <span>⚠️ 风险评估与建议 </span>
                  <a-tag :color="getRiskLevelColor(devicePredictionData.riskAssessmentAndAdvice?.riskLevel)">
                    {{ getRiskLevelText(devicePredictionData.riskAssessmentAndAdvice?.riskLevel) }}
                  </a-tag>
                </template>
                <div class="risk-assessment-advice">
                  <div class="risk-reason">
                    <p><strong>🔍 风险原因分析：</strong></p>
                    <p class="reason-text">{{ devicePredictionData.riskAssessmentAndAdvice?.riskReason }}</p>
                  </div>
                  <div class="advice" style="margin-top: 16px;">
                    <p><strong>💡 预测建议：</strong></p>
                    <p class="advice-text">{{ devicePredictionData.riskAssessmentAndAdvice?.advice }}</p>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 加载状态 -->
        <div v-if="!devicePredictionData.hasData" class="loading-state">
          <a-spin size="large" />
          <p style="margin-top: 16px; text-align: center;">正在加载预测数据...</p>
        </div>
      </div>
    </a-modal>
  </PageWrapper>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { PageWrapper } from '@/components/Page';
import { useECharts } from '@/hooks/web/useECharts';
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  LineChartOutlined,
  BarChartOutlined,
  ExportOutlined,
  UpOutlined,
  DownOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';

defineOptions({
  name: 'SplitterPortWarning'
});

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const detailModalVisible = ref(false);

const allPredictionLoading = ref(false);
const devicePredictionModalVisible = ref(false);
const refreshPredictionLoading = ref(false);

const trendChartRef = ref<HTMLDivElement>();
const devicePredictionChartRef = ref<HTMLDivElement>();
const trendChartCollapsed = ref(false);

// 统计数据
const statistics = reactive({
  normalDevices: 156,
  attentionDevices: 23,
  alarmDevices: 5,
  totalDevices: 184,
  lastUpdateTime: '2024-01-15 14:30:25'
});

// 筛选条件
const filters = reactive({
  deviceCode: '',
  region: '',
  deviceStatus: '',
  occupancyRate: '',        // 实占率范围：'0-50', '50-80', '80-95', '95-100'
  freePortCount: '',        // 空闲端口数：'0', '1', '2-10', '10+'
  installAfterTime: null as any    // 入网时间：选择年月后的设备
});

// 筛选选项配置
const occupancyRateOptions = [
  { label: '全部', value: '' },
  { label: '0-50%', value: '0-50' },
  { label: '50-80%', value: '50-80' },
  { label: '80-95%', value: '80-95' },
  { label: '95-100%', value: '95-100' }
];

const freePortOptions = [
  { label: '全部', value: '' },
  { label: '0个', value: '0' },
  { label: '1个', value: '1' },
  { label: '2-10个', value: '2-10' },
  { label: '10个以上', value: '10+' }
];


// 趋势图配置
const trendConfig = reactive({
  searchCode: '',
  currentDevice: '',
  timeRange: 'month' as 'month' | 'quarter'
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 设备列表数据
const deviceList = ref<DeviceContainer[]>([]);

// 分光器详情数据
const splitterDetails = ref<SplitterDetail[]>([]);



// 设备预测详情数据
const selectedDeviceForPrediction = ref<DeviceContainer | null>(null);
const devicePredictionData = ref<DevicePredictionAnalysis>({
  hasData: false
} as DevicePredictionAnalysis);

// 数据类型定义
interface DeviceContainer {
  设备编码: string;
  区域: string;
  小区入库时间: string;
  覆盖的工程级的线路到达房间数: number;
  分光器数: number;
  分光器容量: number;
  分光器空闲数: number;
  ftth终端数: number;
  // 计算字段
  实占率: number;
  健康状态: 'normal' | 'attention' | 'alarm';
  // 预测字段
  预测实占率?: number;
  预测空闲数?: number;
  预测状态?: 'normal' | 'attention' | 'alarm' | 'expansion';
}

interface SplitterDetail {
  分光器编码: string;
  安装地址: string;
  分光器容量: number;
  分光器空闲数: number;
  实占率: number;
  入网时间: string;
  数据更新时间: string;
  健康状态: 'normal' | 'attention' | 'alarm';
}



interface DevicePredictionAnalysis {
  hasData: boolean;
  设备编码?: string;
  historicalData?: Array<{
    week: string;
    occupancyRate: number;
  }>;

  // 预测结果模块
  predictionResults?: {
    nextWeekRate: number;        // 下周预测实占率
    nextMonthRate: number;       // 下月预测实占率
    trend: string;               // 趋势：上升/下降/平稳/数据不足
  };

  // 风险评估与建议模块（合并）
  riskAssessmentAndAdvice?: {
    riskLevel: string;           // 风险等级：high/medium/low/数据不足
    riskReason: string;          // 一句话风险原因
    advice: string;              // 简化建议
  };
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '设备编码',
    dataIndex: '设备编码',
    key: '设备编码',
    width: 180,
    fixed: 'left'
  },
  {
    title: '区域',
    dataIndex: '区域',
    key: '区域',
    width: 100,
    fixed: 'left'
  },
  {
    title: '实占率',
    dataIndex: '实占率',
    key: '实占率',
    width: 120,
    align: 'center'
  },
  {
    title: '健康状态',
    dataIndex: '健康状态',
    key: '健康状态',
    width: 100,
    align: 'center'
  },
  {
    title: '分光器数',
    dataIndex: '分光器数',
    key: '分光器数',
    width: 100,
    align: 'center'
  },
  {
    title: '分光器容量',
    dataIndex: '分光器容量',
    key: '分光器容量',
    width: 120,
    align: 'center'
  },
  {
    title: '分光器空闲数',
    dataIndex: '分光器空闲数',
    key: '分光器空闲数',
    width: 130,
    align: 'center'
  },
  {
    title: '预测实占率',
    dataIndex: '预测实占率',
    key: '预测实占率',
    width: 120,
    align: 'center'
  },
  {
    title: '预测空闲数',
    dataIndex: '预测空闲数',
    key: '预测空闲数',
    width: 120,
    align: 'center'
  },
  {
    title: '预测状态',
    dataIndex: '预测状态',
    key: '预测状态',
    width: 100,
    align: 'center'
  },
  {
    title: 'FTTH终端数',
    dataIndex: 'ftth终端数',
    key: 'ftth终端数',
    width: 120,
    align: 'center'
  },
  {
    title: '覆盖房间数',
    dataIndex: '覆盖的工程级的线路到达房间数',
    key: '覆盖的工程级的线路到达房间数',
    width: 120,
    align: 'center'
  },
  {
    title: '小区入库时间',
    dataIndex: '小区入库时间',
    key: '小区入库时间',
    width: 130
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
    align: 'left'
  }
];

// 分光器详情表格列定义
const detailColumns: TableColumnsType = [
  {
    title: '分光器编码',
    dataIndex: '分光器编码',
    key: '分光器编码',
    width: 150
  },
  {
    title: '安装地址',
    dataIndex: '安装地址',
    key: '安装地址',
    width: 200,
    ellipsis: true
  },
  {
    title: '分光器容量',
    dataIndex: '分光器容量',
    key: '分光器容量',
    width: 120,
    align: 'center'
  },
  {
    title: '分光器空闲数',
    dataIndex: '分光器空闲数',
    key: '分光器空闲数',
    width: 130,
    align: 'center'
  },
  {
    title: '实占率',
    dataIndex: '实占率',
    key: '实占率',
    width: 150,
    align: 'center'
  },
  {
    title: '入网时间',
    dataIndex: '入网时间',
    key: '入网时间',
    width: 120
  },
  {
    title: '数据更新时间',
    dataIndex: '数据更新时间',
    key: '数据更新时间',
    width: 150
  },
  {
    title: '健康状态',
    dataIndex: '健康状态',
    key: '健康状态',
    width: 100,
    align: 'center'
  }
];

// 方法定义
const goBack = () => {
  router.back();
};

const refreshData = async () => {
  loading.value = true;
  try {
    await loadDeviceList();
    message.success('数据刷新成功');
  } catch (error) {
    message.error('数据刷新失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = async () => {
  pagination.current = 1;
  await loadDeviceList();
};

const handleReset = () => {
  filters.deviceCode = '';
  filters.region = '';
  filters.deviceStatus = '';
  filters.occupancyRate = '';
  filters.freePortCount = '';
  filters.installAfterTime = null;
  handleSearch();
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadDeviceList();
};

const showDeviceDetail = async (record: DeviceContainer) => {
  try {
    loading.value = true;
    // 模拟API调用获取分光器详情
    splitterDetails.value = await mockGetSplitterDetails(record.设备编码);
    detailModalVisible.value = true;
  } catch (error) {
    message.error('获取设备详情失败');
  } finally {
    loading.value = false;
  }
};









const handleExport = () => {
  message.info('导出功能开发中...');
};

// 全部预测方法
const executeAllPrediction = async () => {
  try {
    allPredictionLoading.value = true;
    message.info('开始执行全部设备预测...');

    // 模拟批量预测API调用
    const predictions = await mockGetAllDevicesPrediction();

    // 更新设备列表的预测数据
    deviceList.value = deviceList.value.map(device => {
      const prediction = predictions.find(p => p.设备编码 === device.设备编码);
      if (prediction) {
        return {
          ...device,
          预测实占率: prediction.预测实占率,
          预测空闲数: prediction.预测空闲数,
          预测状态: prediction.预测状态
        };
      }
      return device;
    });

    message.success(`预测完成！共预测 ${predictions.length} 个设备`);
  } catch (error) {
    message.error('全部预测执行失败');
  } finally {
    allPredictionLoading.value = false;
  }
};

// 显示设备预测详情
const showDevicePrediction = async (record: DeviceContainer) => {
  try {
    selectedDeviceForPrediction.value = record;
    devicePredictionModalVisible.value = true;
    devicePredictionData.value = { hasData: false } as DevicePredictionAnalysis;

    // 加载设备预测详情数据
    devicePredictionData.value = await mockGetDevicePredictionDetail(record.设备编码);

    // 等待DOM更新后初始化图表
    await nextTick();
    // 添加额外的延迟确保模态框完全渲染
    setTimeout(() => {
      initDevicePredictionChart();
    }, 200);
  } catch (error) {
    message.error('获取设备预测详情失败');
  }
};

// 处理设备预测模态框关闭
const handleDevicePredictionModalClose = () => {
  // 重置数据
  devicePredictionData.value = { hasData: false } as DevicePredictionAnalysis;
  selectedDeviceForPrediction.value = null;
};

// 重新预测设备
const refreshDevicePrediction = async () => {
  if (!selectedDeviceForPrediction.value) return;

  try {
    refreshPredictionLoading.value = true;

    // 重置数据状态，显示加载中
    devicePredictionData.value = { hasData: false } as DevicePredictionAnalysis;

    // 重新获取预测数据
    devicePredictionData.value = await mockGetDevicePredictionDetail(
      selectedDeviceForPrediction.value.设备编码
    );

    // 重新初始化图表
    await nextTick();
    setTimeout(() => {
      initDevicePredictionChart();
    }, 200);

    message.success('预测数据已更新');
  } catch (error) {
    message.error('重新预测失败');
  } finally {
    refreshPredictionLoading.value = false;
  }
};

// 趋势图相关方法
const toggleTrendChart = () => {
  trendChartCollapsed.value = !trendChartCollapsed.value;
};

const onSearchTrend = (searchCode: string) => {
  if (!searchCode.trim()) {
    message.warning('请输入设备编码或分光器编码');
    return;
  }

  trendConfig.currentDevice = searchCode.trim();
  loadTrendData();
};

const onTimeRangeChange = () => {
  if (trendConfig.currentDevice) {
    loadTrendData();
  }
};

const clearTrendSelection = () => {
  trendConfig.searchCode = '';
  trendConfig.currentDevice = '';
  trendData.value = [];
};

// 工具方法

const getProgressColor = (rate: number) => {
  if (rate >= 90) return '#ff4d4f';
  if (rate >= 80) return '#faad14';
  return '#52c41a';
};

const getStatusColor = (status: string) => {
  const colors = {
    normal: 'green',
    attention: 'orange',
    alarm: 'red'
  };
  return colors[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    attention: '注意',
    alarm: '告警'
  };
  return texts[status] || '未知';
};

const getTrendColor = (trend: string) => {
  const colors = {
    // 英文格式（兼容旧数据）
    increasing: 'red',
    stable: 'blue',
    decreasing: 'green',
    // 中文格式（新数据）
    '上升': 'red',
    '平稳': 'blue',
    '下降': 'green',
    '数据不足': 'gray'
  };
  return colors[trend] || 'default';
};

const getTrendText = (trend: string) => {
  const texts = {
    // 英文格式转中文（兼容旧数据）
    increasing: '上升',
    stable: '平稳',
    decreasing: '下降',
    // 中文格式直接返回（新数据）
    '上升': '上升',
    '平稳': '平稳',
    '下降': '下降',
    '数据不足': '数据不足'
  };
  return texts[trend] || '未知';
};

const getPriorityColor = (priority: string) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  };
  return colors[priority] || 'default';
};

const getPriorityText = (priority: string) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  };
  return texts[priority] || '未知';
};

// 计算实占率
const calculateOccupancyRate = (record: any) => {
  if (!record.分光器容量 || record.分光器容量 === 0) return 0;
  const rate = 1 - (record.分光器空闲数 / record.分光器容量);
  return Math.max(0, Math.min(100, Math.round(rate * 100)));
};

// 计算健康状态
const calculateHealthStatus = (occupancyRate: number) => {
  if (occupancyRate >= 90) return 'alarm';     // 告警
  if (occupancyRate >= 80) return 'attention'; // 注意
  return 'normal';                             // 正常
};

// 预测状态相关方法
const getPredictionStatusColor = (status: string) => {
  const colors = {
    normal: 'green',
    attention: 'orange',
    alarm: 'red',
    expansion: 'purple'
  };
  return colors[status] || 'default';
};

const getPredictionStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    attention: '注意',
    alarm: '告警',
    expansion: '扩容建议'
  };
  return texts[status] || '未知';
};

const getRiskLevelColor = (level: string) => {
  const colors = {
    // 英文格式（兼容旧数据）
    low: 'green',
    medium: 'orange',
    high: 'red',
    // 中文格式（新数据）
    '低': 'green',
    '中': 'orange',
    '高': 'red',
    '数据不足': 'gray'
  };
  return colors[level] || 'default';
};

const getRiskLevelText = (level: string) => {
  const texts = {
    // 英文格式转中文（兼容旧数据）
    low: '低风险',
    medium: '中等风险',
    high: '高风险',
    // 中文格式处理（新数据）
    '低': '低风险',
    '中': '中等风险',
    '高': '高风险',
    '数据不足': '无法评估'
  };
  return texts[level] || '未知';
};

// 数据加载方法
const loadDeviceList = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    const response = await mockGetDeviceList();
    deviceList.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    message.error('加载设备列表失败');
  } finally {
    loading.value = false;
  }
};

// 图表初始化
const { setOptions: setTrendOptions } = useECharts(trendChartRef);
const { setOptions: setDevicePredictionOptions } = useECharts(devicePredictionChartRef);

// 趋势图数据
const trendData = ref<TrendData[]>([]);

interface TrendData {
  deviceCode: string;
  data: Array<{
    week: string;
    occupancyRate: number;
  }>;
}

// 加载趋势数据
const loadTrendData = async () => {
  if (!trendConfig.currentDevice) return;

  try {
    loading.value = true;
    trendData.value = await mockGetTrendData(
      [trendConfig.currentDevice],
      trendConfig.timeRange
    );

    nextTick(() => {
      initTrendChart();
    });
  } catch (error) {
    message.error('加载趋势数据失败');
  } finally {
    loading.value = false;
  }
};

// 初始化趋势图
const initTrendChart = () => {
  if (!trendData.value.length) return;

  const deviceData = trendData.value[0];
  const weeks = deviceData.data.map(item => item.week);
  const occupancyRates = deviceData.data.map(item => item.occupancyRate);

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.axisValue}<br/>实占率: ${param.value}%`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: weeks,
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      name: '实占率',
      type: 'line',
      data: occupancyRates,
      smooth: true,
      lineStyle: {
        color: '#1890ff',
        width: 3
      },
      symbol: 'circle',
      symbolSize: 8,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: '#1890ff40'
          }, {
            offset: 1,
            color: '#1890ff10'
          }]
        }
      }
    }]
  };

  setTrendOptions(option);
};

// 初始化设备预测图表
const initDevicePredictionChart = () => {
  console.log('initDevicePredictionChart 被调用');
  console.log('devicePredictionData.value:', devicePredictionData.value);

  if (!devicePredictionData.value.hasData || !devicePredictionData.value.historicalData) {
    console.warn('设备预测数据不存在', {
      hasData: devicePredictionData.value.hasData,
      historicalData: devicePredictionData.value.historicalData
    });
    return;
  }

  // 确保DOM元素存在
  if (!devicePredictionChartRef.value) {
    console.warn('设备预测图表DOM元素不存在');
    return;
  }

  console.log('开始初始化设备预测详情图表');
  try {

  const historicalData = devicePredictionData.value.historicalData;
  const weeks = historicalData.map(item => item.week);
  const occupancyRates = historicalData.map(item => item.occupancyRate);

  // 添加预测数据点
  const predictionWeeks = ['第5周', '第6周'];
  const predictionRates = [
    devicePredictionData.value.prediction?.nextWeekRate || 0,
    devicePredictionData.value.prediction?.nextMonthRate || 0
  ];

  const allWeeks = [...weeks, ...predictionWeeks];
  const allRates = [...occupancyRates, ...Array(predictionWeeks.length).fill(null)];
  const predictionData = [...Array(weeks.length).fill(null), ...predictionRates];

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          if (param.value !== null) {
            result += `${param.marker}${param.seriesName}: ${param.value}%<br/>`;
          }
        });
        return result;
      }
    },
    legend: {
      data: ['历史数据', '预测数据'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: allWeeks,
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '历史数据',
        type: 'line',
        data: allRates,
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 3
        },
        symbol: 'circle',
        symbolSize: 8,
        connectNulls: false
      },
      {
        name: '预测数据',
        type: 'line',
        data: predictionData,
        smooth: true,
        lineStyle: {
          color: '#ff4d4f',
          width: 3,
          type: 'dashed'
        },
        symbol: 'diamond',
        symbolSize: 10,
        connectNulls: false
      }
    ]
  };

    setDevicePredictionOptions(option);
    console.log('设备预测详情图表初始化成功');
  } catch (error) {
    console.error('初始化设备预测图表失败:', error);
    message.error('图表初始化失败');
  }
};



// 模拟API方法
const mockGetDeviceList = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 原始数据（模拟数据库数据）
  const rawData = [
    {
      设备编码: 'CZ-FG-001',
      区域: '常州',
      小区入库时间: '2023-03-15',
      覆盖的工程级的线路到达房间数: 320,
      分光器数: 8,
      分光器容量: 256,
      分光器空闲数: 45,
      ftth终端数: 185
    },
    {
      设备编码: 'CZ-FG-002',
      区域: '常州',
      小区入库时间: '2023-05-20',
      覆盖的工程级的线路到达房间数: 280,
      分光器数: 6,
      分光器容量: 192,
      分光器空闲数: 78,
      ftth终端数: 95
    },
    {
      设备编码: 'WX-FG-001',
      区域: '无锡',
      小区入库时间: '2022-11-10',
      覆盖的工程级的线路到达房间数: 450,
      分光器数: 12,
      分光器容量: 384,
      分光器空闲数: 12,
      ftth终端数: 298
    },
    {
      设备编码: 'SZ-FG-001',
      区域: '苏州',
      小区入库时间: '2023-08-10',
      覆盖的工程级的线路到达房间数: 380,
      分光器数: 10,
      分光器容量: 320,
      分光器空闲数: 95,
      ftth终端数: 156
    }
  ];

  // 计算实占率和健康状态
  const mockData: DeviceContainer[] = rawData.map(item => {
    const occupancyRate = calculateOccupancyRate(item);
    const healthStatus = calculateHealthStatus(occupancyRate);

    return {
      ...item,
      实占率: occupancyRate,
      健康状态: healthStatus
    };
  });

  return {
    data: mockData,
    total: mockData.length
  };
};

const mockGetSplitterDetails = async (deviceCode: string) => {
  await new Promise(resolve => setTimeout(resolve, 500));

  // 原始分光器数据
  const rawDetails = [
    {
      分光器编码: `${deviceCode}-SP-001`,
      安装地址: '1号机房A架',
      分光器容量: 32,
      分光器空闲数: 8,
      入网时间: '2023-03-15',
      数据更新时间: '2024-01-15 14:25:30'
    },
    {
      分光器编码: `${deviceCode}-SP-002`,
      安装地址: '1号机房B架',
      分光器容量: 32,
      分光器空闲数: 2,
      入网时间: '2023-03-15',
      数据更新时间: '2024-01-15 14:25:30'
    },
    {
      分光器编码: `${deviceCode}-SP-003`,
      安装地址: '2号机房A架',
      分光器容量: 64,
      分光器空闲数: 1,
      入网时间: '2023-03-15',
      数据更新时间: '2024-01-15 14:25:30'
    }
  ];

  // 计算实占率和健康状态
  const mockDetails: SplitterDetail[] = rawDetails.map(item => {
    const occupancyRate = calculateOccupancyRate(item);
    const healthStatus = calculateHealthStatus(occupancyRate);

    return {
      ...item,
      实占率: occupancyRate,
      健康状态: healthStatus
    };
  });

  return mockDetails;
};





// 模拟趋势数据API
const mockGetTrendData = async (deviceCodes: string[], timeRange: string) => {
  await new Promise(resolve => setTimeout(resolve, 800));

  const getWeeksCount = (range: string) => {
    switch (range) {
      case 'month': return 4;   // 最近30天 = 4周
      case 'quarter': return 12; // 最近90天 = 12周
      default: return 4;
    }
  };

  const weeksCount = getWeeksCount(timeRange);

  // 生成周次数组（从第1周到第N周）
  const weeks = Array.from({ length: weeksCount }, (_, i) => {
    const weekNumber = i + 1;
    return `第${weekNumber}周`;
  });

  // 为每个设备生成趋势数据
  const trendData: TrendData[] = deviceCodes.map(deviceCode => {
    // 根据设备编码生成不同的基础占用率
    const baseRate = deviceCode.includes('CZ') ? 75 :
                    deviceCode.includes('WX') ? 85 : 65;

    const data = weeks.map((week, index) => {
      // 生成有趋势的随机数据
      const trend = Math.sin(index * 0.3) * 8; // 周期性波动
      const random = (Math.random() - 0.5) * 12; // 随机波动
      const occupancyRate = Math.max(0, Math.min(100, baseRate + trend + random));

      return {
        week,
        occupancyRate: Math.round(occupancyRate * 100) / 100
      };
    });

    return {
      deviceCode,
      data
    };
  });

  return trendData;
};

// 模拟全部设备预测API
const mockGetAllDevicesPrediction = async () => {
  await new Promise(resolve => setTimeout(resolve, 2000));

  const predictions = deviceList.value.map(device => {
    const currentRate = device.实占率;
    const predictedRate = Math.min(100, currentRate + Math.floor(Math.random() * 15) + 5);
    const predictedFreeCount = Math.max(0, device.分光器容量 - Math.floor(device.分光器容量 * predictedRate / 100));

    let predictedStatus: 'normal' | 'attention' | 'alarm' | 'expansion' = 'normal';
    if (predictedRate >= 95) predictedStatus = 'expansion';
    else if (predictedRate >= 90) predictedStatus = 'alarm';
    else if (predictedRate >= 80) predictedStatus = 'attention';

    return {
      设备编码: device.设备编码,
      预测实占率: predictedRate,
      预测空闲数: predictedFreeCount,
      预测状态: predictedStatus
    };
  });

  return predictions;
};

// 模拟设备预测详情API
const mockGetDevicePredictionDetail = async (deviceCode: string) => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 模拟数据不足的情况（设备编码包含'003'）
  if (deviceCode.includes('003')) {
    const mockDetailInsufficient: DevicePredictionAnalysis = {
      hasData: true,
      设备编码: deviceCode,
      historicalData: [
        { week: '第1周', occupancyRate: 45 }
      ],

      // 预测结果模块（数据不足情况）
      predictionResults: {
        nextWeekRate: 45,
        nextMonthRate: 45,
        trend: '数据不足'
      },

      // 风险评估与建议模块（数据不足情况）
      riskAssessmentAndAdvice: {
        riskLevel: '数据不足',
        riskReason: '历史数据严重不足，预测结果仅供参考，误差可能很大。',
        advice: '建议收集更多历史数据后再进行预测分析。⚠️ 警告：数据点不足，使用简单平均预测，结果仅供参考。'
      }
    };
    return mockDetailInsufficient;
  }

  // 模拟中等风险的情况（设备编码包含'002'）
  if (deviceCode.includes('002')) {
    const mockDetailMedium: DevicePredictionAnalysis = {
      hasData: true,
      设备编码: deviceCode,
      historicalData: [
        { week: '第1周', occupancyRate: 72 },
        { week: '第2周', occupancyRate: 74 },
        { week: '第3周', occupancyRate: 76 },
        { week: '第4周', occupancyRate: 78 }
      ],

      // 预测结果模块（中等风险）
      predictionResults: {
        nextWeekRate: 80,
        nextMonthRate: 83,
        trend: '上升'
      },

      // 风险评估与建议模块（中等风险）
      riskAssessmentAndAdvice: {
        riskLevel: 'medium',
        riskReason: '当前实占率78.0%较高，空闲端口数12个充足，潜在需求比45.8%适中，入网时间2022-08-20。',
        advice: '基于实占率上升趋势，建议关注设备负载，定期监控设备状态。'
      }
    };
    return mockDetailMedium;
  }

  // 模拟低风险的情况（设备编码包含'001'）
  if (deviceCode.includes('001')) {
    const mockDetailLow: DevicePredictionAnalysis = {
      hasData: true,
      设备编码: deviceCode,
      historicalData: [
        { week: '第1周', occupancyRate: 45 },
        { week: '第2周', occupancyRate: 47 },
        { week: '第3周', occupancyRate: 46 },
        { week: '第4周', occupancyRate: 48 }
      ],

      // 预测结果模块（低风险）
      predictionResults: {
        nextWeekRate: 49,
        nextMonthRate: 52,
        trend: '平稳'
      },

      // 风险评估与建议模块（低风险）
      riskAssessmentAndAdvice: {
        riskLevel: 'low',
        riskReason: '当前实占率48.0%正常，空闲端口数25个充足，潜在需求比32.1%较低，入网时间2021-05-10。',
        advice: '实占率保持平稳，继续监控即可。'
      }
    };
    return mockDetailLow;
  }

  // 默认高风险情况
  const mockDetail: DevicePredictionAnalysis = {
    hasData: true,
    设备编码: deviceCode,
    historicalData: [
      { week: '第1周', occupancyRate: 65 },
      { week: '第2周', occupancyRate: 70 },
      { week: '第3周', occupancyRate: 75 },
      { week: '第4周', occupancyRate: 82 }
    ],

    // 预测结果模块（高风险）
    predictionResults: {
      nextWeekRate: 87,
      nextMonthRate: 95,
      trend: '上升'
    },

    // 风险评估与建议模块（高风险）
    riskAssessmentAndAdvice: {
      riskLevel: 'high',
      riskReason: '当前实占率82.0%较高，空闲端口数8个较少，潜在需求比65.2%较高，入网时间2023-01-15。',
      advice: '基于实占率上升趋势，建议在1个月内进行扩容，重点监控设备负载变化。'
    }
  };

  return mockDetail;
};

// 生命周期
onMounted(() => {
  loadDeviceList();
});
</script>

<style scoped lang="less">
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .back-btn {
      padding: 4px 8px;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }
  }
}

.statistics-cards {
  margin-bottom: 16px;

  .stat-card {
    height: 100px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.normal {
      border-left: 4px solid #52c41a;
    }

    &.attention {
      border-left: 4px solid #faad14;
    }

    &.alarm {
      border-left: 4px solid #ff4d4f;
    }

    &.total {
      border-left: 4px solid #1890ff;
    }
  }

  .update-time {
    margin-top: 12px;
    text-align: center;
    color: #8c8c8c;
    font-size: 14px;

    .anticon {
      margin-right: 8px;
    }
  }
}

.trend-chart-card {
  margin-bottom: 16px;

  .trend-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .collapse-btn {
      padding: 4px 8px;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  .trend-chart-content {
    .trend-config {
      margin-bottom: 16px;
      padding: 16px;
      background-color: #fafafa;
      border-radius: 6px;
      border: 1px solid #f0f0f0;

      :deep(.ant-form-item) {
        margin-bottom: 8px;
      }
    }

    .chart-display {
      .chart-container {
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        padding: 16px;
        background-color: #fff;

        .chart-header {
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;

          .device-info {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }
        }
      }

      .empty-chart {
        text-align: center;
        padding: 60px 0;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        background-color: #fafafa;
      }
    }
  }
}

.filter-card {
  margin-bottom: 16px;

  :deep(.ant-form-item) {
    margin-bottom: 8px;
  }

  :deep(.ant-form-item-label) {
    font-weight: 500;
  }
}

.table-card {
  .occupancy-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .ant-progress {
      flex: 1;
      margin: 0;
    }

    .occupancy-text {
      font-weight: 500;
      min-width: 40px;
    }
  }



  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #e6f7ff !important;
  }

  .no-prediction {
    color: #8c8c8c;
    font-style: italic;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0;
    min-width: 160px; // 确保有足够的宽度容纳三个按钮

    .action-btn {
      min-width: 48px;
      text-align: center;
      padding: 4px 8px;
      margin-right: 4px;

      &:last-child {
        margin-right: 0;
      }
    }

    .action-btn-placeholder {
      min-width: 48px;
      height: 22px; // 与按钮高度保持一致
      display: inline-block;
      margin-right: 4px;
    }
  }
}

.prediction-content {
  .prediction-config {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;

    :deep(.ant-form-item) {
      margin-bottom: 8px;
    }
  }

  .chart-container {
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;
  }

  .prediction-info {
    .highlight {
      font-weight: 600;
      color: #1890ff;
    }

    .text-warning {
      color: #faad14;
      font-weight: 600;
    }

    .text-danger {
      color: #ff4d4f;
      font-weight: 600;
    }

    .ant-card {
      height: 100%;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.device-prediction-content {
  .device-info-header {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }

  .prediction-chart-container {
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;
    background-color: #fff;
    min-height: 382px; // 确保容器有固定高度

    .chart-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 350px;
    }
  }

  .device-prediction-info {
    .highlight {
      font-weight: 600;
      color: #1890ff;
    }

    .ant-card {
      height: 100%;
    }

    // 预测结果模块样式
    .prediction-results {
      p {
        margin-bottom: 12px;
        font-size: 14px;

        strong {
          color: #262626;
        }

        .highlight {
          font-size: 16px;
          margin-left: 8px;
        }
      }
    }

    // 风险评估与建议模块样式
    .risk-assessment-advice {
      .risk-reason {
        margin-bottom: 16px;

        .reason-text {
          color: #595959;
          line-height: 1.6;
          margin-top: 8px;
          padding: 8px 12px;
          background-color: #fafafa;
          border-radius: 4px;
          border-left: 3px solid #1890ff;
        }
      }

      .advice {
        .advice-text {
          color: #595959;
          line-height: 1.6;
          margin-top: 8px;
          padding: 8px 12px;
          background-color: #f6ffed;
          border-radius: 4px;
          border-left: 3px solid #52c41a;
        }
      }

      p {
        margin-bottom: 8px;

        strong {
          color: #262626;
        }
      }
    }
  }

  .loading-state {
    text-align: center;
    padding: 60px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .statistics-cards {
    :deep(.ant-col) {
      margin-bottom: 8px;
    }
  }

  .filter-card {
    :deep(.ant-form) {
      .ant-form-item {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}
</style>
