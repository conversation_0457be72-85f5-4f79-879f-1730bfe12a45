# 分光器预测API接口使用说明

## 概述

`splitter_prediction_api.py` 是基于原始预测代码 `hybridmodel_predictor_modular.py` 封装的RESTful API接口，用于为前后端提供预测分析服务。

## 核心特点

- ✅ **完全保留原始预测逻辑** - 核心算法代码未做任何修改
- ✅ **RESTful API设计** - 标准的HTTP接口，易于集成
- ✅ **详细的接口文档** - 每个接口都有完整的请求/响应格式说明
- ✅ **错误处理机制** - 完善的异常处理和日志记录
- ✅ **跨域支持** - 支持前端跨域请求
- ✅ **健康检查** - 提供服务状态监控接口

## 安装依赖

```bash
pip install flask flask-cors pandas numpy scikit-learn tensorflow statsmodels
```

## 启动服务

```bash
python splitter_prediction_api.py
```

服务将在 `http://localhost:5000` 启动

## API接口列表

### 1. 健康检查接口

**接口地址：** `GET /api/health`

**功能：** 检查API服务是否正常运行

**响应示例：**
```json
{
    "status": "healthy",
    "service": "splitter-prediction-api",
    "version": "1.0.0",
    "tensorflow_available": true,
    "timestamp": "2024-12-25T10:30:00"
}
```

### 2. 单设备预测接口

**接口地址：** `POST /api/predict/single`

**功能：** 对单个设备进行预测分析，对应后端 `SplitterService.predictSingleDevice()` 方法

**请求格式：**
```json
{
    "deviceCode": "CZ-FG-001",
    "period": "week",
    "historicalData": [
        {
            "实占率": 0.85,
            "空闲端口数": 8,
            "分光器容量": 48,
            "潜在需求比": 0.6,
            "ftth终端数": 120,
            "覆盖的工程级的线路到达房间数": 200
        }
    ]
}
```

**响应格式：**
```json
{
    "hasData": true,
    "设备编码": "CZ-FG-001",
    "prediction": {
        "nextWeekRate": 87.5,
        "nextMonthRate": 89.2,
        "trend": "increasing",
        "confidence": 85.0
    },
    "recommendations": {
        "expansionAdvice": "建议在2周内增加分光器容量",
        "adjustmentAdvice": "可考虑将部分用户迁移至邻近设备",
        "priority": "medium"
    },
    "historicalData": [...],
    "deviceType": "normal",
    "trendAnalysis": "业务增长趋势 | 实占率85.0% | ✅ 运行正常"
}
```

### 3. 批量预测接口

**接口地址：** `POST /api/predict/batch`

**功能：** 批量预测多个设备，对应后端 `SplitterService.predictAllDevices()` 方法

**请求格式：**
```json
{
    "devices": [
        {
            "deviceCode": "CZ-FG-001",
            "historicalData": [...]
        },
        {
            "deviceCode": "CZ-FG-002",
            "historicalData": [...]
        }
    ]
}
```

**响应格式：**
```json
[
    {
        "设备编码": "CZ-FG-001",
        "预测实占率": 87.5,
        "预测空闲数": 6,
        "预测状态": "attention",
        "deviceType": "stable"
    },
    {
        "设备编码": "CZ-FG-002",
        "预测实占率": 95.2,
        "预测空闲数": 2,
        "预测状态": "alarm",
        "deviceType": "volatile"
    }
]
```

### 4. 趋势分析接口

**接口地址：** `POST /api/analyze/trend`

**功能：** 分析设备使用趋势，对应后端趋势数据分析功能

**请求格式：**
```json
{
    "deviceCode": "CZ-FG-001",
    "timeRange": "recent",
    "historicalData": [...]
}
```

**响应格式：**
```json
{
    "deviceCode": "CZ-FG-001",
    "trendAnalysis": "📈 业务增长趋势 | 🎯 实占率85.0% | ✅ 运行正常",
    "deviceType": "normal",
    "trendData": [
        {"week": "第1周", "occupancyRate": 85.2},
        {"week": "第2周", "occupancyRate": 86.1},
        {"week": "第3周", "occupancyRate": 87.0}
    ]
}
```

### 5. 风险评估接口

**接口地址：** `POST /api/analyze/risk`

**功能：** 评估设备风险等级，对应后端风险评估功能

**请求格式：**
```json
{
    "deviceCode": "CZ-FG-001",
    "currentData": {
        "实占率": 0.85,
        "空闲端口数": 8,
        "分光器容量": 48,
        "潜在需求比": 0.6
    }
}
```

**响应格式：**
```json
{
    "deviceCode": "CZ-FG-001",
    "riskScore": 75.5,
    "riskLevel": "medium",
    "reasons": ["实占率较高（≥ 80%）", "空闲端口数较少（4-5个）"],
    "recommendations": "建议制定扩容计划，定期检查设备负载"
}
```

### 6. 配置获取接口

**接口地址：** `GET /api/config`

**功能：** 获取预测算法配置参数

**响应格式：**
```json
{
    "healthThresholds": {
        "attention": 80,
        "alarm": 90
    },
    "predictionConfig": {
        "algorithm": "hybrid",
        "confidence": 85
    },
    "supportedDeviceTypes": ["stable", "expanded", "normal", "volatile", "insufficient_data"],
    "apiVersion": "1.0.0",
    "tensorflowAvailable": true
}
```

## 与后端Java代码的对接

### 在SplitterServiceImpl中调用Python API

```java
// 在SplitterServiceImpl.java中添加HTTP客户端调用
@Override
public JSONObject executePredictionAlgorithm(List<JSONObject> historicalData, String period) {
    try {
        // 构造请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("deviceCode", deviceCode);
        requestData.put("period", period);
        requestData.put("historicalData", historicalData);
        
        // 调用Python API
        String apiUrl = "http://localhost:5000/api/predict/single";
        String response = httpClient.post(apiUrl, requestData.toJSONString());
        
        // 解析响应
        JSONObject result = JSON.parseObject(response);
        return result.getJSONObject("prediction");
        
    } catch (Exception e) {
        log.error("调用预测API失败", e);
        throw new RuntimeException("预测算法执行失败: " + e.getMessage());
    }
}
```

### HTTP客户端配置

```java
// 添加HTTP客户端依赖和配置
@Component
public class PredictionApiClient {
    
    private final RestTemplate restTemplate;
    
    public PredictionApiClient() {
        this.restTemplate = new RestTemplate();
    }
    
    public JSONObject callPredictionApi(String endpoint, JSONObject requestData) {
        String url = "http://localhost:5000" + endpoint;
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(requestData.toJSONString(), headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        
        return JSON.parseObject(response.getBody());
    }
}
```

## 部署建议

### 开发环境
- 直接运行 `python splitter_prediction_api.py`
- API地址：`http://localhost:5000`

### 生产环境
- 使用 Gunicorn 部署：`gunicorn -w 4 -b 0.0.0.0:5000 splitter_prediction_api:app`
- 配置 Nginx 反向代理
- 添加负载均衡和健康检查

### Docker部署
```dockerfile
FROM python:3.8-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY splitter_prediction_api.py .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "splitter_prediction_api:app"]
```

## 注意事项

1. **数据格式**：确保传入的历史数据包含必要的字段
2. **错误处理**：API会返回详细的错误信息，便于调试
3. **性能考虑**：批量预测时建议控制设备数量，避免超时
4. **日志监控**：API提供详细的日志输出，便于监控和调试
5. **版本兼容**：API接口设计考虑了向后兼容性

## 技术支持

如有问题，请检查：
1. Python依赖是否正确安装
2. API服务是否正常启动
3. 请求数据格式是否正确
4. 网络连接是否正常
