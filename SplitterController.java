package com.telecom.nrm.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.telecom.common.biyi.BiyiPageRequest;
import com.telecom.common.biyi.BiyiPageResult;
import com.telecom.common.util.JSONObjectUtil;
import com.telecom.da.client.domain.PageResponse;
import com.telecom.nrm.dao.SplitterDao;
import com.telecom.nrm.dao.RegionDao;
import com.telecom.nrm.domain.NRMConstants;
import com.telecom.nrm.domain.Region;
import com.telecom.nrm.service.SplitterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分光器端口预警管理控制器
 * 对应前端 splitter-port.vue 页面的所有功能接口
 * 
 * 主要功能：
 * 1. 设备列表查询和分页
 * 2. 统计数据获取
 * 3. 设备详情查询
 * 4. 预测分析功能
 * 5. 趋势数据查询
 * 6. 数据导出功能
 * 7. 区域管理
 * 8. 预警处理
 */
@RestController
@RequestMapping("/api/splitter")
@Slf4j
public class SplitterController {

    @Autowired
    SplitterService splitterService;

    @Autowired
    SplitterDao splitterDao;

    @Autowired
    RegionDao regionDao;

    /**
     * 获取统计数据
     * 🎭 对应前端 statistics 演示数据
     * 返回正常设备、注意设备、告警设备、总设备数量和最后更新时间
     * 
     * @return 统计数据JSON对象
     */
    @GetMapping("/statistics")
    public ResponseEntity<JSONObject> getStatistics() {
        log.info("REST request to get splitter statistics");
        try {
            JSONObject statistics = splitterService.getStatistics();
            return ResponseEntity.ok().body(statistics);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "获取统计数据失败");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取设备列表（支持分页和筛选）
     * 🎭 对应前端 deviceList 演示数据
     * 支持按设备编码、区域、设备状态、日期范围等条件筛选
     * 
     * @param example 查询条件参数
     * @param pageable 分页参数
     * @return 分页的设备列表数据
     */
    @GetMapping("/devices")
    public BiyiPageResult<JSONObject> getDevices(@RequestParam(required = false) Map example, BiyiPageRequest pageable) {
        log.info("REST request to get device list with params: {}, page: {}", example, pageable);
        
        JSONObject jsonObject = (JSONObject) JSON.toJSON(example);
        List<JSONObject> deviceList = new ArrayList<>();
        
        try {
            // 调用DAO层查询设备列表数据
            // 🔌 后端对接：这里需要调用实际的数据库查询API
            PageResponse<JSONObject> devicePage = splitterDao.queryDeviceList(
                jsonObject, 
                pageable.getSize(), 
                pageable.getPage(), 
                NRMConstants.AREA_NAME_WLYY_DATABSE_MAP.get(jsonObject.getString("ds"))
            );
            
            if (devicePage != null && devicePage.getData() != null && !devicePage.getData().isEmpty()) {
                // 转换大数字为字符串，避免前端精度丢失
                JSONObjectUtil.convertBigNumberToString(devicePage.getData());
                deviceList.addAll(devicePage.getData());
            }
            
            // 📊 计算每个设备的实占率和健康状态
            for (JSONObject device : deviceList) {
                // 调用Service层计算实占率
                double occupancyRate = splitterService.calculateOccupancyRate(device);
                device.put("实占率", occupancyRate);
                
                // 调用Service层计算健康状态
                String healthStatus = splitterService.calculateHealthStatus(occupancyRate);
                device.put("健康状态", healthStatus);
            }
            
            return new BiyiPageResult<>(
                deviceList, 
                devicePage.getPageInfo().getTotalCount(), 
                devicePage.getPageInfo().getPageSize()
            );
            
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            return new BiyiPageResult<>(new ArrayList<>(), 0, pageable.getSize());
        }
    }

    /**
     * 获取设备分光器详情
     * 🎭 对应前端 splitterDetails 演示数据
     * 用于设备详情弹窗显示该设备下所有分光器的详细信息
     * 
     * @param deviceCode 设备编码
     * @return 分光器详情列表
     */
    @GetMapping("/device/{deviceCode}/splitters")
    public ResponseEntity<List<JSONObject>> getSplitterDetails(@PathVariable String deviceCode) {
        log.info("REST request to get splitter details for device: {}", deviceCode);
        
        try {
            List<JSONObject> splitterDetails = splitterService.getSplitterDetails(deviceCode);
            return ResponseEntity.ok().body(splitterDetails);
        } catch (Exception e) {
            log.error("获取设备分光器详情失败，设备编码: {}", deviceCode, e);
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    /**
     * 单设备预测分析
     * 🎭 对应前端 predictionData 演示数据
     * 根据设备编码和预测周期进行预测分析
     * 
     * @param request 预测请求参数，包含deviceCode和period
     * @return 预测分析结果
     */
    @PostMapping("/predict/single")
    public ResponseEntity<JSONObject> predictSingleDevice(@RequestBody JSONObject request) {
        log.info("REST request to predict single device: {}", request);
        
        try {
            JSONObject predictionResult = splitterService.predictSingleDevice(request);
            return ResponseEntity.ok().body(predictionResult);
        } catch (Exception e) {
            log.error("单设备预测分析失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("hasData", false);
            errorResponse.put("error", "预测分析失败");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取设备预测详情
     * 🎭 对应前端 devicePredictionData 演示数据
     * 用于操作栏"预测"按钮弹窗，提供更详细的预测信息
     * 
     * @param deviceCode 设备编码
     * @return 设备预测详情
     */
    @GetMapping("/device/{deviceCode}/prediction-detail")
    public ResponseEntity<JSONObject> getDevicePredictionDetail(@PathVariable String deviceCode) {
        log.info("REST request to get device prediction detail for: {}", deviceCode);
        
        try {
            JSONObject predictionDetail = splitterService.getDevicePredictionDetail(deviceCode);
            return ResponseEntity.ok().body(predictionDetail);
        } catch (Exception e) {
            log.error("获取设备预测详情失败，设备编码: {}", deviceCode, e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("hasData", false);
            errorResponse.put("error", "获取预测详情失败");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 批量预测所有设备
     * 🎭 对应前端批量预测功能
     * 对所有设备执行预测分析，更新预测结果
     * 
     * @return 批量预测结果列表
     */
    @PostMapping("/predict/batch")
    public ResponseEntity<List<JSONObject>> predictAllDevices() {
        log.info("REST request to predict all devices");
        
        try {
            List<JSONObject> batchPredictionResults = splitterService.predictAllDevices();
            return ResponseEntity.ok().body(batchPredictionResults);
        } catch (Exception e) {
            log.error("批量预测失败", e);
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    /**
     * 获取趋势数据
     * 🎭 对应前端 trendData 演示数据
     * 根据设备编码和时间范围查询历史趋势数据
     * 
     * @param deviceCode 设备编码
     * @param timeRange 时间范围（month/quarter）
     * @return 趋势数据
     */
    @GetMapping("/trend")
    public ResponseEntity<List<JSONObject>> getTrendData(
            @RequestParam String deviceCode,
            @RequestParam String timeRange) {
        log.info("REST request to get trend data for device: {}, timeRange: {}", deviceCode, timeRange);
        
        try {
            JSONObject params = new JSONObject();
            params.put("deviceCode", deviceCode);
            params.put("timeRange", timeRange);
            
            List<JSONObject> trendData = splitterService.getTrendData(params);
            return ResponseEntity.ok().body(trendData);
        } catch (Exception e) {
            log.error("获取趋势数据失败", e);
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    /**
     * 数据导出功能
     * 🎭 对应前端导出功能
     * 根据筛选条件导出设备数据为Excel文件
     * 
     * @param example 导出条件参数
     * @param response HTTP响应对象
     */
    @GetMapping("/export")
    public void exportData(@RequestParam(required = false) Map example, HttpServletResponse response) {
        log.info("REST request to export data with params: {}", example);
        
        try {
            JSONObject params = (JSONObject) JSON.toJSON(example);
            // 🔌 后端对接：调用Service层导出数据
            // 这里需要实现Excel文件生成和下载逻辑
            splitterService.exportData(params, response);
        } catch (Exception e) {
            log.error("数据导出失败", e);
            // 设置错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取区域列表和数据字典
     * 🎭 对应前端区域下拉选项和其他字典数据
     * 
     * @param example 查询参数
     * @return 包含区域列表和其他字典数据的JSON对象
     */
    @GetMapping("/dictionary")
    public ResponseEntity<JSONObject> getDictionary(@RequestParam(required = false) Map example) {
        log.info("REST request to get dictionary data: {}", example);
        
        try {
            JSONObject exampleJSON = (JSONObject) JSON.toJSON(example);
            JSONObject meta = new JSONObject();
            
            // 获取区域列表
            Region regionParam = new Region();
            List<Region> regionList = regionDao.listQuery(regionParam, NRMConstants.SHARDING_CODE);
            List<Region> filteredRegions = regionList.stream()
                .filter(region -> region.getAreaLevelId().equals(Long.parseLong("100700")))
                .collect(Collectors.toList());
            meta.put("regionList", filteredRegions);
            
            // 获取设备状态列表
            List<JSONObject> statusList = new ArrayList<>();
            JSONObject normalStatus = new JSONObject();
            normalStatus.put("statusName", "正常");
            normalStatus.put("statusValue", "normal");
            statusList.add(normalStatus);
            
            JSONObject attentionStatus = new JSONObject();
            attentionStatus.put("statusName", "注意");
            attentionStatus.put("statusValue", "attention");
            statusList.add(attentionStatus);
            
            JSONObject alarmStatus = new JSONObject();
            alarmStatus.put("statusName", "告警");
            alarmStatus.put("statusValue", "alarm");
            statusList.add(alarmStatus);
            
            meta.put("statusList", statusList);
            
            // 获取统计数据
            meta.put("statistics", splitterService.getStatistics());
            
            return ResponseEntity.ok().body(meta);
        } catch (Exception e) {
            log.error("获取字典数据失败", e);
            return ResponseEntity.status(500).body(new JSONObject());
        }
    }

    /**
     * 处理设备预警
     * 🎭 对应前端预警处理功能
     * 
     * @param deviceCode 设备编码
     * @param request 处理请求参数
     * @return 处理结果
     */
    @PostMapping("/device/{deviceCode}/handle-warning")
    public ResponseEntity<JSONObject> handleDeviceWarning(
            @PathVariable String deviceCode,
            @RequestBody JSONObject request) {
        log.info("REST request to handle device warning for: {}, request: {}", deviceCode, request);
        
        try {
            JSONObject result = splitterService.handleDeviceWarning(deviceCode, request);
            return ResponseEntity.ok().body(result);
        } catch (Exception e) {
            log.error("处理设备预警失败，设备编码: {}", deviceCode, e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "处理预警失败");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
