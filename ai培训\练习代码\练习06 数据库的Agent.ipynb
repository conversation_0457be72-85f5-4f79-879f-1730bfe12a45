{"cells": [{"cell_type": "markdown", "id": "bf7da19d-c28e-4f29-adea-86ff9171d83b", "metadata": {}, "source": ["## 练习06 使用AGent控制数据库（手动控制）\n", "\n", "**创建数据库** ：可以是一个空的数据库\n", "\n", "**创建表格并导入数据** ： 向一个空的数据库中导入数据，形成表格。\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "085985d1-a414-4b3c-9f4c-6691fcd7589b", "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine\n", "import pandas as pd\n", "# 创建数据库\n", "database_file_path = \"D:\\\\我的培训\\\\250718 南京电信\\\\sales.db\" #销售数据库\n", "engine = create_engine(f'sqlite:///{database_file_path}')\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2c902425-458c-4ebf-a2c2-4b85c47586b5", "metadata": {}, "outputs": [], "source": ["#把excel导入到数据库中\n", "for file_url in  [\"门店销售\"]:\n", "    df = pd.read_excel('D:\\\\我的培训\\\\250718 南京电信\\\\'+file_url+'.xlsx').fillna(value = 0)\n", "    df.to_sql(\n", "        file_url,\n", "        con=engine,\n", "        if_exists='replace',\n", "        index=False\n", "    )"]}, {"cell_type": "code", "execution_count": 7, "id": "3672803c-fc52-4e37-90cc-3cab410997b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'门店': '门店B', '总销售金额': 660000}, {'门店': '门店A', '总销售金额': 300000}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#使用sql语句，查询数据表\n", "import sqlite3\n", "def search_from_db(query):\n", "    conn = sqlite3.connect(database_file_path) #和数据库做连接 conn : connect \n", "    cursor = conn.cursor()\n", "    \n", "    #query='''SELECT \n", "    #    门店,\n", "    #    SUM(销售金额) AS 总销售金额\n", "    #FROM 门店销售\n", "    #GROUP BY 门店\n", "    #ORDER BY 总销售金额 DESC; '''\n", "    \n", "    cursor.execute(query) #执行查询语句\n", "    rows = cursor.fetchall() #获取返回的结果\n", "    column_names = [column[0] for column in cursor.description]\n", "    results = [dict(zip(column_names, row)) for row in rows]\n", "    \n", "    cursor.close()\n", "    conn.close()\n", "    \n", "    return results"]}, {"cell_type": "code", "execution_count": 11, "id": "b7c3c506-9db4-4b71-bd36-b2b410f1abfe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT 门店 \n", "FROM 门店销售表 \n", "WHERE 产品 = '华为手机' \n", "ORDER BY 销售数量 DESC \n", "LIMIT 1;\n"]}], "source": ["#让LLM帮我们写sql查询语句\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "#创建一个和大模型的连接器\n", "url='https://api.deepseek.com'\n", "llm=ChatOpenAI(model='deepseek-chat',base_url=url, api_key=os.environ[\"DS_API_KEY\"])\n", "from langchain.prompts import ChatPromptTemplate\n", "\n", "prompt1_str='''我提供database里的表结构，请你写一段sql查询语句。\n", "我的查询要求：{user_query}\n", "表名：门店销售\n", " 表结构：id\t门店\t产品\t销售数量\t销售金额\n", "1\t门店A\t华为手机\t35\t140000\n", "2\t门店A\t小米手机\t40\t160000\n", "3\t门店B\t华为手机\t15\t60000\n", "4\t门店B\t苹果手机\t50\t200000\n", "5\t门店B\tvivo手机\t100\t400000\n", "\n", "注意：直接生成sql语句，不要说明，不要解释。'''\n", "node1_prompt_template=ChatPromptTemplate.from_template(prompt1_str)\n", "\n", "chain1=node1_prompt_template | llm\n", "\n", "res=chain1.invoke(\"查询哪个门店的华为手机的销量最高？\")\n", "print(res.content.replace('```sql','').replace('```',''))"]}, {"cell_type": "code", "execution_count": 12, "id": "3c55bd73-16a9-482e-8509-7e45815e2c2b", "metadata": {}, "outputs": [], "source": ["#开始创建Graph.  step1 . 需要哪些变量\n", "from typing_extensions import TypedDict #指定字典格式\n", "from typing import Literal #更严格的类型检查方式\n", "\n", "class TypedDictState(TypedDict): #此类对应的实例就是State\n", "    user_query: str\n", "    sql_query: str\n", "    sql_result: list"]}, {"cell_type": "code", "execution_count": 16, "id": "eaa844cb-f8bf-43fb-b397-f0267f2bd26f", "metadata": {}, "outputs": [], "source": ["#设计Graph\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "def node_1(state):\n", "    print(\"---Node 1 把用户的语言转换为sql语句---\")\n", "    prompt1_str='''我提供database里的表结构，请你写一段sql查询语句。\n", "    我的查询要求：{user_query}\n", "    表名：门店销售\n", "     表结构：id\t门店\t产品\t销售数量\t销售金额\n", "    1\t门店A\t华为手机\t35\t140000\n", "    2\t门店A\t小米手机\t40\t160000\n", "    3\t门店B\t华为手机\t15\t60000\n", "    4\t门店B\t苹果手机\t50\t200000\n", "    5\t门店B\tvivo手机\t100\t400000\n", "    \n", "    注意：直接生成sql语句，不要说明，不要解释。'''\n", "    node1_prompt_template=ChatPromptTemplate.from_template(prompt1_str)\n", "    chain1=node1_prompt_template | llm\n", "    res=chain1.invoke(state[\"user_query\"])\n", "    return {\"sql_query\": res.content.replace('```sql','').replace('```','')}\n", "\n", "def node_2(state):\n", "    print(\"---Node 2 执行查询语句---\")\n", "    conn = sqlite3.connect(database_file_path) #和数据库做连接 conn : connect \n", "    cursor = conn.cursor()\n", "    cursor.execute(state[\"sql_query\"]) #执行查询语句\n", "    rows = cursor.fetchall() #获取返回的结果\n", "    column_names = [column[0] for column in cursor.description]\n", "    results = [dict(zip(column_names, row)) for row in rows]\n", "    cursor.close()\n", "    conn.close()\n", "    return {\"sql_result\": results}\n", "\n", "\n", "# Build graph\n", "builder = StateGraph(TypedDictState)\n", "builder.add_node(\"node_1\", node_1)\n", "builder.add_node(\"node_2\", node_2)\n", "\n", "# Logic\n", "builder.add_edge(START, \"node_1\")\n", "builder.add_edge(\"node_1\",\"node_2\")\n", "builder.add_edge(\"node_2\", END)\n", "\n", "# Add\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 17, "id": "2fae550a-f60a-43ab-8a35-b041b34ec0e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Node 1 把用户的语言转换为sql语句---\n", "---Node 2 执行查询语句---\n", "{'user_query': '查询哪个门店的华为手机销量最高？', 'sql_query': \"\\nSELECT 门店\\nFROM 门店销售\\nWHERE 产品 = '华为手机'\\nORDER BY 销售数量 DESC\\nLIMIT 1;\\n\", 'sql_result': [{'门店': '门店A'}]}\n"]}], "source": ["res=graph.invoke({\"user_query\":'查询哪个门店的华为手机销量最高？'})\n", "print(res)"]}, {"cell_type": "code", "execution_count": 18, "id": "be1250e5-fe88-4589-adac-e40e67d641db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Node 1 把用户的语言转换为sql语句---\n", "---Node 2 执行查询语句---\n", "{'user_query': '按照销量降序排列，统计不同手机的销售额和销售量？', 'sql_query': '\\nSELECT \\n    产品,\\n    SUM(销售数量) AS 销售量,\\n    SUM(销售金额) AS 销售额\\nFROM \\n    门店销售\\nGROUP BY \\n    产品\\nORDER BY \\n    销售量 DESC;\\n', 'sql_result': [{'产品': 'vivo手机', '销售量': 100, '销售额': 400000}, {'产品': '苹果手机', '销售量': 50, '销售额': 200000}, {'产品': '华为手机', '销售量': 50, '销售额': 200000}, {'产品': '小米手机', '销售量': 40, '销售额': 160000}]}\n"]}], "source": ["res=graph.invoke({\"user_query\":'按照销量降序排列，统计不同手机的销售额和销售量？'})\n", "print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "4aab81f5-a2e1-4c5a-b53c-fb57d0dd9ceb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}