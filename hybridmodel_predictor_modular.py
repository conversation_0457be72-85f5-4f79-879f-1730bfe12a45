import os
import pandas as pd
import numpy as np
import warnings
import random
from datetime import datetime, timedelta

# 统计模型
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# 机器学习模型
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.linear_model import LinearRegression
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping, History
from tensorflow.keras.optimizers import Adam

# 抑制警告
warnings.filterwarnings('ignore')

# 检查TensorFlow是否可用
try:
    import tensorflow as tf
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

# 安全整数转换，避免NaN报错
def safe_int(val):
    return int(val) if pd.notna(val) else 0


class DeviceClassifier:
    """设备分类器模块"""

    def classify_device_type(self, data, device_id):
        """基于数据量的两层分类策略"""
        data_points = len(data)

        # 第一层：数据量判断
        min_data_for_advanced = 8

        if data_points < min_data_for_advanced:
            return 'insufficient_data'

        # 第二层：数据充足的设备进行业务特征分类
        # 计算变异系数
        cv = data['实占率'].std() / data['实占率'].mean() if data['实占率'].mean() > 0 else 0

        # 扩容检测
        expansion_status = self._detect_expansion_advanced(data)

        # 业务特征分类
        if expansion_status in ['recent_expansion', 'historical_expansion']:
            return 'expanded'
        elif cv < 0.05:  # 稳定设备阈值
            return 'stable'
        elif cv < 0.15:  # 正常设备阈值
            return 'normal'
        else:  # 波动设备（包含原来的volatile和highly_volatile）
            return 'volatile'

    def _detect_expansion_advanced(self, data):
        """基于occupancy_rate_analysis.py的高级扩容检测"""
        if '容量变化' not in data.columns:
            return 'none'

        # 检查容量变化
        capacity_changes = data['容量变化'].sum()
        initial_capacity = data['分光器容量'].iloc[0] if len(data) > 0 else 1
        capacity_ratio = capacity_changes / initial_capacity if initial_capacity > 0 else 0

        # 检查实占率突降（扩容的典型特征）
        if '实占率变化' in data.columns:
            occupancy_changes = data['实占率变化']
            significant_drops = (occupancy_changes < -0.15).sum()  # 15%的突降
        else:
            significant_drops = 0

        # 检查近期扩容（最近12周）
        recent_weeks = min(12, len(data))
        if recent_weeks > 0:
            recent_data = data.iloc[-recent_weeks:]
            recent_capacity_change = recent_data['容量变化'].sum()
            recent_capacity_ratio = recent_capacity_change / initial_capacity if initial_capacity > 0 else 0

            if recent_capacity_ratio > 0.1:  # 近期容量增加超过10%
                return 'recent_expansion'

        # 检查历史扩容
        if capacity_ratio > 0.1 or significant_drops > 0:
            return 'historical_expansion'

        return 'none'


class TrendAnalyzer:
    """趋势分析器模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def analyze_device_trend(self, data, device_id, device_type='stable'):
        """通用设备趋势分析框架"""
        if len(data) < 3:
            return "数据不足，无法进行趋势分析"

        # 获取实占率序列
        occupancy_series = data['实占率'].values

        # 计算趋势指标
        recent_trend = self._calculate_recent_trend(occupancy_series)
        volatility = np.std(occupancy_series)
        current_level = occupancy_series[-1]

        # 根据设备类型生成分析
        if device_type == 'stable':
            return self._analyze_stable_device(recent_trend, volatility, current_level)
        elif device_type == 'expanded':
            return self._analyze_expanded_device(recent_trend, volatility, current_level, data)
        elif device_type == 'volatile':
            return self._analyze_volatile_device(recent_trend, volatility, current_level)
        else:
            return self._analyze_normal_device(recent_trend, volatility, current_level)

    def _calculate_recent_trend(self, series, window=4):
        """计算最近趋势"""
        if len(series) < window:
            window = len(series)

        recent_data = series[-window:]
        if len(recent_data) < 2:
            return 0

        # 使用线性回归计算趋势
        x = np.arange(len(recent_data))
        slope = np.polyfit(x, recent_data, 1)[0]
        return slope

    def _analyze_stable_device(self, trend, volatility, current_level):
        """分析稳定设备"""
        trend_desc = "稳定" if abs(trend) < 0.01 else ("上升" if trend > 0 else "下降")

        if current_level > 0.8:
            risk_level = "🔴 高风险"
        elif current_level > 0.6:
            risk_level = "🟡 中风险"
        else:
            risk_level = "🟢 低风险"

        return f"📏 业务长期稳定，无明显发展趋势 | ✅ 设备运行稳定 | {risk_level} 当前实占率{'较高，建议关注' if current_level > 0.6 else '正常'}"

    def _analyze_expanded_device(self, trend, volatility, current_level, data):
        """分析扩容设备"""
        # 检查扩容效果
        if '容量变化' in data.columns:
            recent_expansion = data['容量变化'].iloc[-4:].sum() > 0
            expansion_effect = "扩容有效" if current_level < 0.7 else "扩容效果有限"
        else:
            expansion_effect = "扩容状态未知"

        return f"🔧 {expansion_effect} | 📈 {'持续增长' if trend > 0.01 else '趋于稳定'} | 🎯 当前实占率{current_level:.1%}"

    def _analyze_volatile_device(self, trend, volatility, current_level):
        """分析波动设备"""
        volatility_desc = "高波动" if volatility > 0.15 else "中等波动"

        return f"⚡ {volatility_desc}设备 | 📊 {'上升趋势' if trend > 0.01 else ('下降趋势' if trend < -0.01 else '无明显趋势')} | ⚠️ 需密切监控"

    def _analyze_normal_device(self, trend, volatility, current_level):
        """分析正常设备"""
        trend_desc = "增长" if trend > 0.01 else ("下降" if trend < -0.01 else "稳定")

        return f"📈 业务{trend_desc}趋势 | 🎯 实占率{current_level:.1%} | ✅ 运行正常"


class ChangeDetector:
    """突变检测器模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def detect_sudden_change(self, data, window_size=2, threshold=0.15):
        """
        滑动窗口均值差检测突变点
        若有多个突变，优先选择最近的一次突变
        """
        if len(data) < window_size * 2:
            return None, 0

        # 从后往前检测，找到第一个（最近的）突变点
        for i in range(len(data) - window_size, window_size - 1, -1):
            before_mean = data[i-window_size:i].mean()
            after_mean = data[i:i+window_size].mean()
            diff = abs(after_mean - before_mean)

            if diff > threshold:
                return i, diff

        return None, 0

    def extract_trend_before_change(self, data):
        """提取突变前的趋势特征"""
        if len(data) < 3:
            return {'slope': 0, 'volatility': 0, 'direction': 'stable'}

        # 计算趋势斜率
        x = np.arange(len(data))
        slope = np.polyfit(x, data, 1)[0]

        # 计算波动性
        volatility = np.std(data)

        # 判断趋势方向
        if slope > 0.01:
            direction = 'increasing'
        elif slope < -0.01:
            direction = 'decreasing'
        else:
            direction = 'stable'

        return {
            'slope': slope,
            'volatility': volatility,
            'direction': direction
        }

    def predict_with_trend_transfer(self, before_data, after_data, test_length):
        """趋势迁移预测法"""
        if len(before_data) < 2:
            # 数据不足，使用突变后数据的均值
            if len(after_data) > 0:
                return [after_data.mean()] * test_length
            else:
                return [0.5] * test_length

        # 提取突变前趋势
        trend_features = self.extract_trend_before_change(before_data)

        # 获取新的基准水平（突变后的水平）
        if len(after_data) > 0:
            new_baseline = after_data.iloc[-1]  # 使用突变后的最新实占率作为基准
        else:
            new_baseline = before_data.iloc[-1]

        # 基于趋势斜率和新基准水平进行预测
        predictions = []
        for i in range(test_length):
            # 应用趋势到新基准水平
            trend_adjustment = trend_features['slope'] * (i + 1)
            prediction = new_baseline + trend_adjustment

            # 确保预测值在合理范围内
            prediction = max(0, min(1, prediction))
            predictions.append(prediction)

        return predictions


class PredictionEngine:
    """基础预测引擎模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def simple_average_predict(self, data):
        """简单平均预测（用于数据严重不足的设备 <5个数据点）"""
        if len(data) == 0:
            # 如果没有数据，使用默认值
            return {
                'occupancy': 0.5,
                'ports': 24,
                'demand': 0.3
            }

        # 计算平均值
        occupancy_avg = data['实占率'].mean()
        ports_avg = data['空闲端口数'].mean()
        demand_avg = data['潜在需求比'].mean() if '潜在需求比' in data.columns else 0.3

        return {
            'occupancy': occupancy_avg,
            'ports': max(0, safe_int(ports_avg)),
            'demand': demand_avg
        }

    def moving_average_predict(self, data, window=4):
        """移动平均预测（用于稳定设备）"""
        if len(data) < window:
            window = len(data)

        if window == 0:
            return self.simple_average_predict(data)

        # 计算移动平均
        recent_data = data.iloc[-window:]
        occupancy_ma = recent_data['实占率'].mean()
        ports_ma = recent_data['空闲端口数'].mean()
        demand_ma = recent_data['潜在需求比'].mean() if '潜在需求比' in recent_data.columns else 0.3

        return {
            'occupancy': occupancy_ma,
            'ports': max(0, safe_int(ports_ma)),
            'demand': demand_ma
        }

    def linear_regression_predict(self, data):
        """线性回归预测"""
        if len(data) < 3:
            return self.moving_average_predict(data)

        X = np.arange(len(data)).reshape(-1, 1)

        # 训练模型
        lr_occupancy = LinearRegression().fit(X, data['实占率'].values)
        lr_ports = LinearRegression().fit(X, data['空闲端口数'].values)
        lr_demand = LinearRegression().fit(X, data['潜在需求比'].values) if '潜在需求比' in data.columns else None

        # 预测下一个时间点
        next_time = len(data)
        pred_occupancy = lr_occupancy.predict([[next_time]])[0]
        pred_occupancy = max(0, min(1, pred_occupancy))

        pred_ports = lr_ports.predict([[next_time]])[0]
        pred_ports = max(0, safe_int(pred_ports))

        if lr_demand is not None:
            pred_demand = lr_demand.predict([[next_time]])[0]
            pred_demand = max(0, pred_demand)
        else:
            pred_demand = 0.3

        return {
            'occupancy': pred_occupancy,
            'ports': pred_ports,
            'demand': pred_demand
        }

    def calculate_terminal_growth_rate(self, data):
        """计算终端数增长率（业务发展趋势指标）"""
        if 'ftth终端数' not in data.columns or len(data) < 2:
            return 0.0

        terminals = data['ftth终端数'].dropna()
        if len(terminals) < 2:
            return 0.0

        # 计算最近几期的平均增长率
        growth_rates = []
        for i in range(1, min(len(terminals), 6)):  # 最多看最近5期的变化
            if terminals.iloc[-i-1] > 0:  # 避免除零
                rate = (terminals.iloc[-i] - terminals.iloc[-i-1]) / terminals.iloc[-i-1]
                growth_rates.append(rate)

        if growth_rates:
            return np.mean(growth_rates)
        else:
            return 0.0


class SpecializedPredictor:
    """专用预测器模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def __init__(self, change_detector, prediction_engine):
        self.change_detector = change_detector
        self.prediction_engine = prediction_engine


class HybridPredictor:
    """
    生产部署版混合预测器类
    完全保留所有预测逻辑，移除训练测试相关内容
    """

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.device_types = {}

        # 初始化模块化组件
        self.device_classifier = DeviceClassifier()
        self.trend_analyzer = TrendAnalyzer()
        self.change_detector = ChangeDetector()
        self.prediction_engine = PredictionEngine()
        self.specialized_predictor = SpecializedPredictor(self.change_detector, self.prediction_engine)

    # ==========================================
    # 1. 设备分类模块
    # ==========================================

    def classify_device_type(self, data, device_id):
        """重定向到模块化组件"""
        return self.device_classifier.classify_device_type(data, device_id)



    # ==========================================
    # 2. 基础预测方法（用于数据不足的设备）
    # ==========================================

    def simple_average_predict(self, data):
        """重定向到模块化组件"""
        return self.prediction_engine.simple_average_predict(data)

    # ==========================================
    # 4. 工具函数
    # ==========================================

    def analyze_device_trend(self, data, device_id, device_type='stable'):
        """重定向到模块化组件"""
        return self.trend_analyzer.analyze_device_trend(data, device_id, device_type)

    def _get_trend_analysis_params(self, device_type):
        """获取不同设备类型的趋势分析参数"""
        params = {
            'stable': {
                'slope_threshold': 0.01,      # 1%变化阈值
                'trend_strength_threshold': 0.7,  # 强趋势阈值
                'weak_trend_threshold': 0.3,      # 弱趋势阈值
                'stability_threshold': 0.02        # 稳定性阈值(2%)
            }
        }
        return params.get(device_type, params['stable'])

    def _generate_trend_conclusion(self, slope, r_value, occupancy_range, occupancy_std,
                                 demand_ratio, occupancy, free_ports, device_type, params):
        """通用趋势结论生成函数"""
        conclusions = []

        # 获取设备类型特定的阈值
        slope_threshold = params['slope_threshold']
        strong_threshold = params['trend_strength_threshold']
        weak_threshold = params['weak_trend_threshold']
        stability_threshold = params['stability_threshold']

        # 趋势强度判断
        trend_strength = abs(r_value)

        # 设备类型特定的趋势描述
        trend_prefix = {
            'stable': '📏 业务长期稳定'
        }.get(device_type, '📊 业务')

        # 基础趋势判断
        if trend_strength > strong_threshold and abs(slope) > slope_threshold:
            if slope > 0:
                conclusions.append("📈 业务稳步增长趋势明显")

                if demand_ratio < 0.35:
                    conclusions.append("💡 用户密度较低，增长空间充足")
                elif demand_ratio > 0.8:
                    conclusions.append("⚠️ 用户密度已较高，需关注容量规划")

                # 扩容建议
                if free_ports <= 2:
                    if demand_ratio < 0.5:
                        conclusions.append("🔧 建议扩容：增长趋势+低用户密度，扩容必要性高")
                    else:
                        conclusions.append("🤔 谨慎扩容：增长趋势但用户密度较高，建议评估实际需求")

            else:  # slope < 0
                conclusions.append("📉 业务呈下降趋势")

                if demand_ratio > 0.6:
                    conclusions.append("💭 用户密度较高但呈下降趋势，可能存在用户流失")
                else:
                    conclusions.append("💭 用户密度和使用率均在下降")

                if free_ports <= 2:
                    conclusions.append("❌ 不建议扩容：下降趋势表明扩容必要性低")

        elif trend_strength > weak_threshold and abs(slope) > slope_threshold/2:
            conclusions.append(f"{trend_prefix}，有轻微变化趋势")
            if slope > 0:
                conclusions.append("↗️ 整体呈缓慢增长")
            else:
                conclusions.append("↘️ 整体呈缓慢下降")

        else:
            # 趋势不明显
            conclusions.append(f"{trend_prefix}，无明显发展趋势")

            if free_ports <= 2:
                if demand_ratio < 0.4:
                    conclusions.append("🔧 建议扩容：虽无明显趋势但用户密度低，存在发展潜力")
                else:
                    conclusions.append("❌ 扩容必要性不高：无明显趋势且用户密度较高")

        # 稳定性评估（根据设备类型调整）
        if occupancy_std < stability_threshold/2:
            conclusions.append("✅ 设备运行非常稳定")
        elif occupancy_std < stability_threshold:
            conclusions.append("✅ 设备运行稳定")
        else:
            conclusions.append(f"⚠️ 设备波动较大，可能需要重新分类")

        # 当前状态评估
        if occupancy > 0.9:
            conclusions.append("🔴 当前实占率很高，需密切监控")
        elif occupancy > 0.8:
            conclusions.append("🟡 当前实占率较高，建议关注")
        elif occupancy < 0.3:
            conclusions.append("🟢 当前实占率较低，容量充足")

        return " | ".join(conclusions)

    def moving_average_predict(self, data, window=4):
        """移动平均预测"""
        if len(data) < window:
            window = len(data)

        # 计算移动平均
        occupancy_ma = data['实占率'].rolling(window=window).mean().iloc[-1]
        ports_ma = data['空闲端口数'].rolling(window=window).mean().iloc[-1]
        demand_ma = data['潜在需求比'].rolling(window=window).mean().iloc[-1]

        return {
            'occupancy': occupancy_ma,
            'ports': int(round(ports_ma)),
            'demand': demand_ma
        }

    # ==========================================
    # 3. 高级预测方法（用于数据充足的设备）
    # ==========================================

    def linear_regression_predict(self, data):
        """重定向到模块化组件"""
        return self.prediction_engine.linear_regression_predict(data)

    def detect_sudden_change(self, data, window_size=2, threshold=0.15):
        """重定向到模块化组件"""
        return self.change_detector.detect_sudden_change(data, window_size, threshold)

    def extract_trend_before_change(self, data):
        """
        提取突变前的趋势特征

        Args:
            data: 突变前的数据

        Returns:
            dict: 趋势特征
        """
        if len(data) < 3:
            return {'slope': 0, 'mean': data.mean(), 'std': data.std()}

        # 计算线性趋势
        x = np.arange(len(data))
        slope, intercept = np.polyfit(x, data, 1)

        # 计算其他统计特征
        mean_val = data.mean()
        std_val = data.std()

        return {
            'slope': slope,
            'intercept': intercept,
            'mean': mean_val,
            'std': std_val
        }

    def predict_with_trend_transfer(self, before_data, after_data, test_length):
        """
        趋势迁移预测法：学习突变前趋势，应用到突变后水平

        Args:
            before_data: 突变前数据（Series）
            after_data: 突变后数据（Series）
            test_length: 需要预测的长度

        Returns:
            array: 预测结果
        """
        # 确保输入是Series或array
        if hasattr(before_data, 'values'):
            before_values = before_data.values
        else:
            before_values = np.array(before_data)

        if hasattr(after_data, 'values'):
            after_values = after_data.values
        else:
            after_values = np.array(after_data)

        # 提取突变前趋势
        trend_features = self.extract_trend_before_change(before_values)

        # 计算突变后的新基准水平
        new_baseline = after_values[-1]  # 使用突变后的最新实占率作为基准

        # 生成预测
        predictions = []
        for i in range(test_length):
            # 基于趋势斜率和新基准水平预测
            pred = new_baseline + trend_features['slope'] * i
            # 限制预测值在合理范围内
            pred = max(0, min(1, pred))  # 实占率应该在0-1之间
            predictions.append(pred)

        return np.array(predictions)

    def calculate_terminal_growth_rate(self, data):
        """
        计算终端数增长率（业务发展趋势指标）
        """
        if 'ftth终端数' not in data.columns or len(data) < 2:
            return 0.0

        terminals = data['ftth终端数'].dropna()
        if len(terminals) < 2:
            return 0.0

        # 计算最近几期的平均增长率
        growth_rates = []
        for i in range(1, min(len(terminals), 6)):  # 最多看最近5期的变化
            if terminals.iloc[-i-1] > 0:  # 避免除零
                rate = (terminals.iloc[-i] - terminals.iloc[-i-1]) / terminals.iloc[-i-1]
                growth_rates.append(rate)

        if growth_rates:
            return np.mean(growth_rates)
        else:
            return 0.0

    def volatile_predict_with_change_detection(self, data, device_id):
        """
        改进的volatile设备预测：基于突变检测的自适应预测
        """
        try:
            # 检查TensorFlow是否可用
            global TENSORFLOW_AVAILABLE

            print(f"  🔍 检测突变点...")

            # 检测突变点
            change_point, change_strength = self.detect_sudden_change(data['实占率'].values)

            if change_point is not None:
                print(f"  📊 检测到突变点: 位置={change_point}, 强度={change_strength:.3f}")

                # 突变型设备处理
                before_change = data.iloc[:change_point]
                after_change = data.iloc[change_point:]

                after_change_length = len(after_change)

                if after_change_length >= 8:
                    # 策略B：突变后数据充足，只用突变后数据训练LSTM
                    print(f"  🎯 策略B: 突变后数据充足({after_change_length}个)，使用突变后LSTM")
                    if TENSORFLOW_AVAILABLE:
                        return self.lstm_predict(after_change, device_id)
                    else:
                        print(f"  ⚠️ TensorFlow不可用，使用线性回归替代")
                        return self.linear_regression_predict(after_change)
                else:
                    # 策略A：突变后数据不足，使用趋势迁移法
                    print(f"  🎯 策略A: 突变后数据不足({after_change_length}个)，使用趋势迁移法")
                    # 使用趋势迁移预测
                    predictions = self.predict_with_trend_transfer(
                        before_change['实占率'],
                        after_change['实占率'],
                        1  # 只预测下一个时间点
                    )

                    # 预测空闲端口数和潜在需求比
                    if '分光器容量' in after_change.columns and len(after_change) > 0:
                        latest_capacity = after_change['分光器容量'].iloc[-1]
                    else:
                        latest_capacity = data['分光器容量'].iloc[-1]

                    # 潜在需求比预测 - 基于业务发展逻辑
                    # 不直接预测潜在需求比，而是基于终端数增长率计算
                    if 'ftth终端数' in data.columns and '覆盖的工程级的线路到达房间数' in data.columns:
                        # 计算终端数增长率
                        terminal_growth_rate = self.calculate_terminal_growth_rate(data)

                        # 基于增长率计算未来潜在需求比
                        current_terminals = data['ftth终端数'].iloc[-1]
                        covered_rooms = data['覆盖的工程级的线路到达房间数'].iloc[-1]
                        predicted_terminals = current_terminals * (1 + terminal_growth_rate)
                        demand = max(0, min(1, predicted_terminals / covered_rooms))

                        print(f"    [INFO] 设备 {device_id} 终端增长率: {terminal_growth_rate:.4f}, 预测潜在需求比: {demand:.4f}")
                    else:
                        # 备选方案：使用当前潜在需求比
                        demand = data['潜在需求比'].iloc[-1] if '潜在需求比' in data.columns else 0.3
                        print(f"    [INFO] 设备 {device_id} 使用当前潜在需求比: {demand:.4f}")

                    # 构造预测结果
                    occupancy = predictions[-1]
                    ports = int(round(latest_capacity * (1 - occupancy)))

                    return {
                        'occupancy': occupancy,
                        'ports': ports,
                        'demand': demand
                    }
            else:
                # 非突变型设备，继续使用LSTM
                print(f"  📈 无突变检测，使用标准LSTM预测")
                if TENSORFLOW_AVAILABLE:
                    return self.lstm_predict(data, device_id)
                else:
                    print(f"  ⚠️ TensorFlow不可用，使用线性回归替代")
                    return self.linear_regression_predict(data)

        except Exception as e:
            print(f"  ❌ Volatile预测失败: {str(e)}")
            return self.linear_regression_predict(data)

    def lstm_predict(self, data, device_id):
        """LSTM预测（生产版本）"""
        try:
            # 设置随机种子确保预测一致性
            random.seed(42)
            np.random.seed(42)
            tf.random.set_seed(42)
            os.environ['PYTHONHASHSEED'] = '42'
            os.environ['TF_DETERMINISTIC_OPS'] = '1'

            if len(data) < 6:
                return self.linear_regression_predict(data)

            # 准备数据 - 使用优化后的特征组合
            # 核心特征：实占率、空闲端口数、终端数变化率
            # 终端数变化率包含了业务发展趋势和用户密度变化信息
            available_features = ['实占率', '空闲端口数']

            # 优先使用终端数变化率（包含业务发展和用户密度信息）
            if '终端数变化率' in data.columns:
                available_features.append('终端数变化率')
                print(f"  使用终端数变化率（包含业务发展趋势和用户密度变化）")
            elif '潜在需求比' in data.columns:
                available_features.append('潜在需求比')
                print(f"  备选：使用潜在需求比")

            features = available_features

            X = []
            y_occupancy = []
            y_ports = []
            y_business_trend = []  # 使用业务趋势替代潜在需求比

            seq_length = min(3, len(data) - 1)

            # 创建序列数据
            for i in range(len(data) - seq_length):
                X.append(data[features].iloc[i:i+seq_length].values)
                y_occupancy.append(data['实占率'].iloc[i+seq_length])
                y_ports.append(data['空闲端口数'].iloc[i+seq_length])

                # 根据可用特征选择目标变量
                if '终端数变化率' in data.columns:
                    y_business_trend.append(data['终端数变化率'].iloc[i+seq_length])
                else:
                    y_business_trend.append(data['潜在需求比'].iloc[i+seq_length])

            if len(X) < 2:
                return self.linear_regression_predict(data)

            X = np.array(X)
            y_occupancy = np.array(y_occupancy)
            y_ports = np.array(y_ports)
            y_business_trend = np.array(y_business_trend)

            # 数据标准化
            scaler_X = MinMaxScaler()
            scaler_y_occ = MinMaxScaler()
            scaler_y_ports = StandardScaler()
            scaler_y_trend = StandardScaler()  # 终端数变化率可能有负值，使用StandardScaler

            X_scaled = scaler_X.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
            y_occ_scaled = scaler_y_occ.fit_transform(y_occupancy.reshape(-1, 1)).flatten()
            y_ports_scaled = scaler_y_ports.fit_transform(y_ports.reshape(-1, 1)).flatten()
            y_trend_scaled = scaler_y_trend.fit_transform(y_business_trend.reshape(-1, 1)).flatten()

            # 构建LSTM模型 - 使用优化后的参数
            model = Sequential([
                LSTM(64, activation='relu', input_shape=(seq_length, len(features))),
                Dropout(0.05),
                Dense(8, activation='relu'),
                Dense(3, activation='linear')
            ])

            model.compile(optimizer=Adam(learning_rate=0.005), loss='mse')

            # 准备训练数据
            y_combined = np.column_stack([y_occ_scaled, y_ports_scaled, y_trend_scaled])

            # 训练模型 - 使用优化后的训练轮数
            model.fit(
                X_scaled, y_combined,
                epochs=100,
                batch_size=1,
                verbose=0,
                validation_split=0.2 if len(X_scaled) > 5 else 0
            )

            # 预测
            last_sequence = data[features].iloc[-seq_length:].values.reshape(1, seq_length, len(features))
            last_sequence_scaled = scaler_X.transform(last_sequence.reshape(-1, len(features))).reshape(last_sequence.shape)

            prediction_scaled = model.predict(last_sequence_scaled, verbose=0)[0]

            # 反标准化
            pred_occupancy = scaler_y_occ.inverse_transform([[prediction_scaled[0]]])[0][0]
            pred_ports = scaler_y_ports.inverse_transform([[prediction_scaled[1]]])[0][0]
            pred_trend = scaler_y_trend.inverse_transform([[prediction_scaled[2]]])[0][0]

            # 应用约束
            pred_occupancy = max(0, min(1, pred_occupancy))
            pred_ports = max(0, int(round(pred_ports)))

            # 基于业务发展逻辑计算潜在需求比
            if '终端数变化率' in data.columns:
                # 使用预测的终端数变化率计算潜在需求比
                terminal_growth_rate = pred_trend
                if 'ftth终端数' in data.columns and '覆盖的工程级的线路到达房间数' in data.columns:
                    current_terminals = data['ftth终端数'].iloc[-1]
                    covered_rooms = data['覆盖的工程级的线路到达房间数'].iloc[-1]
                    predicted_terminals = current_terminals * (1 + terminal_growth_rate)
                    pred_demand = max(0, min(1, predicted_terminals / covered_rooms))
                else:
                    pred_demand = data['潜在需求比'].iloc[-1] if '潜在需求比' in data.columns else 0.3
            else:
                # 备选方案：使用基于历史数据计算的增长率
                terminal_growth_rate = self.calculate_terminal_growth_rate(data)
                if 'ftth终端数' in data.columns and '覆盖的工程级的线路到达房间数' in data.columns:
                    current_terminals = data['ftth终端数'].iloc[-1]
                    covered_rooms = data['覆盖的工程级的线路到达房间数'].iloc[-1]
                    predicted_terminals = current_terminals * (1 + terminal_growth_rate)
                    pred_demand = max(0, min(1, predicted_terminals / covered_rooms))
                else:
                    pred_demand = data['潜在需求比'].iloc[-1] if '潜在需求比' in data.columns else 0.3

            return {
                'occupancy': pred_occupancy,
                'ports': pred_ports,
                'demand': pred_demand
            }

        except Exception as e:
            print(f"LSTM预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)

    def exponential_smoothing_predict(self, data):
        """
        指数平滑预测（专门用于扩容设备）
        """
        if len(data) < 4:
            return self.linear_regression_predict(data)

        try:
            # 对实占率进行指数平滑
            occupancy_series = data['实占率'].values

            # 使用简单指数平滑
            from statsmodels.tsa.holtwinters import SimpleExpSmoothing

            # 实占率预测
            model_occ = SimpleExpSmoothing(occupancy_series)
            fitted_occ = model_occ.fit(smoothing_level=0.3)
            pred_occupancy = fitted_occ.forecast(1)[0]
            pred_occupancy = max(0, min(1, pred_occupancy))

            # 空闲端口数预测（考虑扩容后的容量）
            latest_capacity = data['分光器容量'].iloc[-1]
            pred_ports = int(latest_capacity * (1 - pred_occupancy))
            pred_ports = max(0, pred_ports)

            # 潜在需求比预测（使用趋势外推）
            demand_series = data['潜在需求比'].values
            if len(demand_series) >= 3:
                # 计算趋势
                x = np.arange(len(demand_series))
                slope, intercept = np.polyfit(x, demand_series, 1)
                pred_demand = slope * len(demand_series) + intercept
                pred_demand = max(0, pred_demand)
            else:
                pred_demand = demand_series[-1] if len(demand_series) > 0 else 0.5

            return {
                'occupancy': pred_occupancy,
                'ports': pred_ports,
                'demand': pred_demand
            }

        except Exception as e:
            print(f"指数平滑预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)

    def improved_expanded_predict(self, data, device_id):
        """改进的expanded设备预测（基于扩容后数据+趋势继承）"""
        try:
            # 1. 检测扩容信息
            expansion_info = self.detect_expansion_points(data)

            if not expansion_info['has_expansion']:
                # 没有扩容，使用标准指数平滑
                print(f"  ⚠️ 未检测到扩容，使用标准指数平滑")
                return self.exponential_smoothing_predict(data)

            # 2. 分析扩容前趋势
            pre_expansion_trend = self.analyze_pre_expansion_trend(data, expansion_info)

            # 3. 获取扩容后数据
            post_expansion_data = self.get_post_expansion_data(data, expansion_info)

            # 4. 基于扩容后数据进行预测（增强版）
            if len(post_expansion_data) >= 2:
                # 扩容后数据可用，使用增强预测
                base_prediction = self.predict_from_post_expansion_data(post_expansion_data)

                # 检测扩容后趋势
                post_expansion_trend = self.detect_post_expansion_trend(post_expansion_data)

                # 5. 应用扩容前趋势修正（仅在扩容后数据充足时）
                if len(post_expansion_data) >= 3:
                    final_prediction = self.apply_trend_correction(base_prediction, pre_expansion_trend, post_expansion_data)
                else:
                    final_prediction = base_prediction  # 数据不足时不进行趋势修正

            else:
                # 扩容后数据不足，使用扩容前趋势+扩容后基准
                final_prediction = self.predict_with_trend_inheritance(data, expansion_info, pre_expansion_trend)
                post_expansion_trend = 'insufficient_data'

            # 6. 计算其他指标
            capacity = data['分光器容量'].iloc[-1]
            pred_ports = int(capacity * (1 - final_prediction))
            pred_demand = data['潜在需求比'].iloc[-1]

            # 7. 详细的扩容分析信息
            expansion_weeks = [data.iloc[point]['周'] for point in expansion_info['expansion_points']] if 'expansion_points' in expansion_info else []
            print(f"  🔧 扩容预测: 扩容点={expansion_info['latest_expansion_point']}, 扩容后数据={len(post_expansion_data)}点")
            print(f"       扩容前趋势={pre_expansion_trend}, 扩容后趋势={post_expansion_trend}")
            if expansion_weeks:
                print(f"       扩容时间: {expansion_weeks}")

            return {
                'occupancy': final_prediction,
                'ports': pred_ports,
                'demand': pred_demand
            }

        except Exception as e:
            print(f"改进扩容预测失败: {e}")
            import traceback
            traceback.print_exc()
            # 回退到标准指数平滑
            return self.exponential_smoothing_predict(data)

    def detect_expansion_points(self, data):
        """检测扩容点"""
        capacity_values = data['分光器容量'].values
        expansion_points = []

        for i in range(1, len(capacity_values)):
            if capacity_values[i] > capacity_values[i-1] * 1.05:  # 容量增加超过5%
                expansion_points.append(i)

        return {
            'has_expansion': len(expansion_points) > 0,
            'expansion_points': expansion_points,
            'latest_expansion_point': expansion_points[-1] if expansion_points else None
        }

    def analyze_pre_expansion_trend(self, data, expansion_info):
        """分析扩容前趋势"""
        if not expansion_info['has_expansion']:
            return 'stable'

        latest_expansion = expansion_info['latest_expansion_point']

        # 获取扩容前3-5个数据点
        pre_start = max(0, latest_expansion - 5)
        pre_end = latest_expansion

        if pre_end - pre_start < 2:
            return 'stable'

        pre_data = data.iloc[pre_start:pre_end]
        occupancy_values = pre_data['实占率'].values

        # 计算趋势
        from scipy import stats
        time_points = np.arange(len(occupancy_values))
        slope, _, r_value, _, _ = stats.linregress(time_points, occupancy_values)

        # 判断趋势类型
        if abs(r_value) > 0.7 and slope > 0.02:
            return 'increasing'  # 明显增长
        elif abs(r_value) > 0.7 and slope < -0.02:
            return 'decreasing'  # 明显下降
        elif abs(slope) > 0.01:
            return 'slight_change'  # 轻微变化
        else:
            return 'stable'  # 稳定

    def get_post_expansion_data(self, data, expansion_info):
        """获取扩容后数据"""
        if not expansion_info['has_expansion']:
            return data

        latest_expansion = expansion_info['latest_expansion_point']
        return data.iloc[latest_expansion:]

    def detect_post_expansion_trend(self, post_expansion_data):
        """检测扩容后趋势"""
        occupancy_values = post_expansion_data['实占率'].values

        if len(occupancy_values) >= 5:
            # 数据充足，分析趋势
            recent_values = occupancy_values[-5:]  # 最近5个数据点
            time_points = np.arange(len(recent_values))

            from scipy import stats
            slope, _, r_value, _, _ = stats.linregress(time_points, recent_values)

            # 趋势分类
            if abs(r_value) > 0.7:  # 强趋势
                if slope > 0.02:
                    return 'strong_growth'      # 强增长
                elif slope < -0.02:
                    return 'strong_decline'     # 强下降
                else:
                    return 'stable'             # 稳定
            elif abs(slope) > 0.01:
                return 'moderate_change'        # 中等变化
            else:
                return 'stable'                 # 稳定

        elif len(occupancy_values) >= 2:
            # 数据有限，简单判断
            latest_change = occupancy_values[-1] - occupancy_values[0]
            weeks = len(occupancy_values)
            weekly_change = latest_change / weeks if weeks > 0 else 0

            if weekly_change > 0.05:
                return 'rapid_growth'
            elif weekly_change < -0.05:
                return 'rapid_decline'
            else:
                return 'stable'

        else:
            return 'insufficient_data'

    def predict_from_post_expansion_data(self, post_expansion_data):
        """基于扩容后数据预测（增强版）"""
        occupancy_values = post_expansion_data['实占率'].values

        # 1. 检测扩容后趋势
        trend = self.detect_post_expansion_trend(post_expansion_data)

        # 2. 根据趋势选择预测策略
        if trend == 'strong_growth':
            # 强增长趋势，使用线性外推 + 增长衰减
            return self.predict_with_growth_extrapolation(post_expansion_data)

        elif trend == 'strong_decline':
            # 强下降趋势，使用衰减模型
            return self.predict_with_decline_model(post_expansion_data)

        elif trend == 'rapid_growth' and len(occupancy_values) < 5:
            # 数据少但增长快，保守预测 + 增长修正
            base_prediction = np.mean(occupancy_values)
            growth_adjustment = 0.05  # 5%的增长预期
            prediction = min(1.0, base_prediction + growth_adjustment)
            return prediction

        elif trend == 'rapid_decline' and len(occupancy_values) < 5:
            # 数据少但下降快，保守预测 + 下降修正
            base_prediction = np.mean(occupancy_values)
            decline_adjustment = -0.03  # 3%的下降预期
            prediction = max(0.0, base_prediction + decline_adjustment)
            return prediction

        else:
            # 稳定或数据不足，使用标准指数平滑
            return self.standard_exponential_smoothing_prediction(post_expansion_data)

    def predict_with_growth_extrapolation(self, post_expansion_data):
        """增长外推预测"""
        occupancy_values = post_expansion_data['实占率'].values

        if len(occupancy_values) >= 3:
            # 计算增长趋势
            from scipy import stats
            time_points = np.arange(len(occupancy_values))
            slope, intercept, _, _, _ = stats.linregress(time_points, occupancy_values)

            # 预测下一期，但增长衰减
            next_time = len(occupancy_values)

            # 增长衰减因子（避免无限增长）
            decay_factor = 0.7  # 增长强度衰减30%
            adjusted_slope = slope * decay_factor
            prediction = adjusted_slope * next_time + intercept

            return max(0, min(1, prediction))
        else:
            return np.mean(occupancy_values)

    def predict_with_decline_model(self, post_expansion_data):
        """下降模型预测"""
        occupancy_values = post_expansion_data['实占率'].values

        if len(occupancy_values) >= 3:
            # 计算下降趋势
            from scipy import stats
            time_points = np.arange(len(occupancy_values))
            slope, intercept, _, _, _ = stats.linregress(time_points, occupancy_values)

            # 预测下一期，但下降缓解
            next_time = len(occupancy_values)

            # 下降缓解因子（避免过度下降）
            relief_factor = 0.8  # 下降强度缓解20%
            adjusted_slope = slope * relief_factor
            prediction = adjusted_slope * next_time + intercept

            return max(0, min(1, prediction))
        else:
            return np.mean(occupancy_values)

    def standard_exponential_smoothing_prediction(self, post_expansion_data):
        """标准指数平滑预测"""
        occupancy_values = post_expansion_data['实占率'].values

        if len(occupancy_values) >= 3:
            # 使用指数平滑
            try:
                from statsmodels.tsa.holtwinters import SimpleExpSmoothing
                model = SimpleExpSmoothing(occupancy_values)
                fitted = model.fit(smoothing_level=0.5)
                prediction = fitted.forecast(1)[0]
            except:
                # 如果statsmodels不可用，使用简单指数平滑
                alpha = 0.5
                smoothed_values = [occupancy_values[0]]

                for i in range(1, len(occupancy_values)):
                    smoothed_value = alpha * occupancy_values[i] + (1 - alpha) * smoothed_values[-1]
                    smoothed_values.append(smoothed_value)

                prediction = smoothed_values[-1]
        else:
            # 数据不足，使用平均值（保守预测）
            prediction = np.mean(occupancy_values)

        return max(0, min(1, prediction))

    def apply_trend_correction(self, base_prediction, pre_expansion_trend, post_expansion_data):
        """应用扩容前趋势修正"""
        if pre_expansion_trend == 'stable':
            return base_prediction

        # 计算扩容后的时间长度
        weeks_after_expansion = len(post_expansion_data)

        # 根据扩容前趋势调整预测
        if pre_expansion_trend == 'increasing':
            # 扩容前增长，扩容后可能继续缓慢增长
            trend_adjustment = 0.01 * weeks_after_expansion  # 每周增长1%
            return min(1, base_prediction + trend_adjustment)
        elif pre_expansion_trend == 'decreasing':
            # 扩容前下降，扩容后可能继续缓慢下降
            trend_adjustment = -0.005 * weeks_after_expansion  # 每周下降0.5%
            return max(0, base_prediction + trend_adjustment)
        elif pre_expansion_trend == 'slight_change':
            # 轻微变化，小幅调整
            trend_adjustment = 0.005 * weeks_after_expansion  # 每周增长0.5%
            return min(1, base_prediction + trend_adjustment)

        return base_prediction

    def predict_with_trend_inheritance(self, data, expansion_info, pre_expansion_trend):
        """扩容后数据不足时，使用趋势继承预测"""
        latest_expansion = expansion_info['latest_expansion_point']
        post_expansion_data = data.iloc[latest_expansion:]

        if len(post_expansion_data) > 0:
            # 使用扩容后的实际值作为基准
            base_occupancy = post_expansion_data['实占率'].iloc[-1]
        else:
            # 使用扩容前最后一个值作为参考
            pre_expansion_data = data.iloc[:latest_expansion]
            base_occupancy = pre_expansion_data['实占率'].iloc[-1] * 0.6  # 假设扩容后下降40%

        # 根据扩容前趋势调整
        if pre_expansion_trend == 'increasing':
            return min(1, base_occupancy + 0.02)  # 增长趋势，预测小幅上升
        elif pre_expansion_trend == 'decreasing':
            return max(0, base_occupancy - 0.01)  # 下降趋势，预测小幅下降
        else:
            return base_occupancy  # 稳定趋势，保持当前水平

    def sarima_predict(self, data):
        """
        SARIMA预测（专门用于normal设备）
        """
        if len(data) < 6:
            return self.linear_regression_predict(data)

        try:
            from statsmodels.tsa.statespace.sarimax import SARIMAX

            # 实占率预测
            occupancy_series = data['实占率'].values

            # 使用简单的SARIMA模型 (1,1,1)
            model_occ = SARIMAX(occupancy_series, order=(1, 1, 1), seasonal_order=(0, 0, 0, 0))
            fitted_occ = model_occ.fit(disp=False)
            pred_occupancy = fitted_occ.forecast(1)[0]
            pred_occupancy = max(0, min(1, pred_occupancy))

            # 空闲端口数预测
            ports_series = data['空闲端口数'].values
            model_ports = SARIMAX(ports_series, order=(1, 1, 1), seasonal_order=(0, 0, 0, 0))
            fitted_ports = model_ports.fit(disp=False)
            pred_ports = fitted_ports.forecast(1)[0]
            pred_ports = max(0, int(round(pred_ports)))

            # 潜在需求比预测
            demand_series = data['潜在需求比'].values
            model_demand = SARIMAX(demand_series, order=(1, 1, 1), seasonal_order=(0, 0, 0, 0))
            fitted_demand = model_demand.fit(disp=False)
            pred_demand = fitted_demand.forecast(1)[0]
            pred_demand = max(0, pred_demand)

            return {
                'occupancy': pred_occupancy,
                'ports': pred_ports,
                'demand': pred_demand
            }

        except Exception as e:
            print(f"SARIMA预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)

    # ==========================================
    # 5. 主预测函数
    # ==========================================

    def predict(self, data, device_id):
        """
        主预测函数：生产部署版本
        """
        # 分类设备类型
        device_type = self.classify_device_type(data, device_id)
        self.device_types[device_id] = device_type

        print(f"设备 {device_id} 分类为: {device_type}")

        # 根据重构后的两层分类策略选择预测方法
        if device_type == 'insufficient_data':
            # 数据不足设备 - 使用简单平均
            prediction = self.simple_average_predict(data)
            print(f"  警告: 数据不足，预测结果仅供参考，误差可能很大")

        elif device_type == 'stable':
            # 稳定设备 - 使用移动平均 + 趋势分析
            prediction = self.moving_average_predict(data)
            # 为stable设备添加趋势分析
            trend_analysis = self.analyze_device_trend(data, device_id, 'stable')
            print(f"  📊 趋势分析: {trend_analysis}")

        elif device_type == 'expanded':
            # 扩容设备 - 使用改进的扩容预测
            prediction = self.improved_expanded_predict(data, device_id)

        elif device_type == 'normal':
            # 正常设备 - 使用SARIMA
            prediction = self.sarima_predict(data)

        elif device_type == 'volatile':
            # 波动设备 - 使用改进的突变检测预测
            prediction = self.volatile_predict_with_change_detection(data, device_id)

        else:  # 其他情况，使用线性回归作为备选
            prediction = self.linear_regression_predict(data)

        return prediction


# 数据加载与预处理函数（从原始hybridmodel.py复制）
def load_and_preprocess_data(folder_path):
    """加载和预处理数据"""
    excel_files = [f for f in os.listdir(folder_path)
                   if f.startswith('三网小区预警_') and f.endswith('.xlsx')]
    all_data = pd.DataFrame()

    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        keep_columns = [
            '所属设备', '小区入库时间', '覆盖的工程级的线路到达房间数',
            '分光器容量', '分光器空闲数', 'ftth终端数', 'now',
            '建筑群', 'address_desc', 'area', '分光器数'
        ]
        existing_columns = [col for col in keep_columns if col in df.columns]
        df = df[existing_columns]
        df['now'] = pd.to_datetime(df['now'], errors='coerce', utc=True).dt.tz_localize(None)

        # 安全处理小区入库时间列
        if '小区入库时间' in df.columns:
            df['小区入库时间'] = pd.to_datetime(df['小区入库时间'], errors='coerce', utc=True).dt.tz_localize(None)
        else:
            print(f"警告：文件 {file} 缺少'小区入库时间'列")
            df['小区入库时间'] = pd.NaT
        all_data = pd.concat([all_data, df], ignore_index=True)
    return all_data

def feature_engineering(all_data):
    """特征工程"""
    all_data['空闲率'] = all_data['分光器空闲数'] / all_data['分光器容量']
    all_data['潜在需求比'] = all_data['ftth终端数'] / (all_data['覆盖的工程级的线路到达房间数'])
    all_data['实占率'] = 1 - all_data['空闲率']
    all_data['空闲端口数'] = all_data['分光器空闲数']

    # 安全计算入库时间差
    try:
        all_data['入库时间差_月'] = ((all_data['now'] - all_data['小区入库时间']).dt.days / 30).round()
    except Exception as e:
        print(f"计算入库时间差时出错: {e}")
        # 使用默认值
        all_data['入库时间差_月'] = 24  # 默认24个月

    # 按设备和时间排序，用于计算变化率
    all_data = all_data.sort_values(['所属设备', 'now'])

    # 计算容量变化
    all_data['容量变化'] = all_data.groupby('所属设备')['分光器容量'].diff().fillna(0)

    # 计算终端数变化率（核心业务特征）
    # 该特征同时包含：1) 业务发展趋势 2) 用户密度变化 3) 市场活跃度
    all_data['终端数变化'] = all_data.groupby('所属设备')['ftth终端数'].diff().fillna(0)

    # 计算终端数变化率（相对变化率）
    prev_terminals = all_data.groupby('所属设备')['ftth终端数'].shift(1)
    all_data['终端数变化率'] = all_data['终端数变化'] / (prev_terminals + 1)  # +1避免除零
    all_data['终端数变化率'] = all_data['终端数变化率'].fillna(0)

    # 终端数变化率的业务含义：
    # > 0: 用户增长，业务发展良好，可能需要更多端口
    # = 0: 用户稳定，业务平稳
    # < 0: 用户流失，业务下滑，端口需求可能减少

    # 注释：业务发展潜力特征已删除，因为终端数变化率已包含相关信息

    return all_data

def aggregate_weekly_data(all_data):
    """按周聚合数据"""
    all_data['week'] = all_data['now'].dt.isocalendar().week
    all_data['周'] = all_data['now'].dt.isocalendar().year.astype(str) + '-' + \
                      all_data['week'].astype(str).str.zfill(2)

    agg_dict = {
        '实占率': 'mean',
        '潜在需求比': 'mean',
        '入库时间差_月': 'mean',
        '分光器容量': 'first',
        '容量变化': 'sum',
        '空闲端口数': 'mean',
        'ftth终端数': 'mean',
        '覆盖的工程级的线路到达房间数': 'first',
        '小区入库时间': 'first',
        '终端数变化': 'sum',  # 周内终端数总变化
        '终端数变化率': 'mean'  # 周内平均变化率（包含业务发展和用户密度信息）
    }

    # 添加额外字段
    for col in ['建筑群', 'address_desc', 'area', '分光器数']:
        if col in all_data.columns:
            agg_dict[col] = 'first'

    weekly_data = all_data.groupby(['所属设备', '周']).agg(agg_dict).reset_index()
    return weekly_data

def calculate_risk_score(occupancy, ports, demand, capacity):
    """计算风险评分"""
    reasons = []

    # 实占率评分
    if occupancy >= 0.97:
        occupancy_score = 100
        reasons.append("实占率极高（≥ 97%）")
    elif occupancy >= 0.93:
        occupancy_score = 90 + (occupancy - 0.93) * 250
        reasons.append("实占率非常高（≥ 93%）")
    elif occupancy >= 0.9:
        occupancy_score = 80 + (occupancy - 0.9) * 333
        reasons.append("实占率偏高（≥ 90%）")
    elif occupancy >= 0.8:
        occupancy_score = 60 + (occupancy - 0.8) * 200
        reasons.append("实占率较高（≥ 80%）")
    else:
        occupancy_score = occupancy * 75

    # 空闲端口数评分
    if ports <= 0:
        ports_score = 100
        reasons.append("无空闲端口")
    elif ports <= 1:
        ports_score = 95
        reasons.append("空闲端口数仅剩 1 个")
    elif ports <= 2:
        ports_score = 85
        reasons.append("空闲端口数仅剩 2 个")
    elif ports <= 3:
        ports_score = 70
        reasons.append("空闲端口数仅剩 3 个")
    elif ports <= 5:
        ports_score = 50
        reasons.append("空闲端口数较少（4-5个）")
    else:
        ports_score = max(0, 50 - (ports - 5) * 5)

    # 潜在需求比评分（修正逻辑：高需求比=用户密度高=端口需求低=风险低）
    if demand >= 0.8:
        demand_score = 10  # 用户密度饱和，端口需求低，风险低
        reasons.append("潜在需求比很高（≥ 80%），用户密度饱和，发展空间有限")
    elif demand >= 0.6:
        demand_score = 20  # 用户密度较高，端口需求较低
        reasons.append("潜在需求比较高（≥ 60%），用户密度较高")
    elif demand >= 0.4:
        demand_score = 30  # 用户密度适中
        reasons.append("潜在需求比适中（40-60%），用户密度正常")
    elif demand >= 0.2:
        demand_score = 50  # 用户密度较低，有发展潜力，可能需要更多端口
        reasons.append("潜在需求比较低（20-40%），存在用户增长空间")
    else:
        demand_score = 70  # 用户密度很低，发展潜力大，端口需求可能增长
        reasons.append("潜在需求比很低（< 20%），业务发展潜力大，需关注端口需求增长")

    # 加权总分
    weights = {'occupancy': 0.5, 'ports': 0.3, 'demand': 0.2}
    total_score = (
        weights['occupancy'] * occupancy_score +
        weights['ports'] * ports_score +
        weights['demand'] * demand_score
    )

    # 特殊规则
    if occupancy >= 0.95 and ports <= 2:
        total_score = max(total_score, 85)
        reasons.append("高实占率+低空闲端口数组合风险")
    elif occupancy >= 0.9 and ports <= 1:
        total_score = max(total_score, 80)
        reasons.append("高实占率+极低空闲端口数组合风险")

    # 确定风险级别
    if total_score >= 80:
        level = '紧急'
    elif total_score >= 60:
        level = '警告'
    elif total_score >= 40:
        level = '注意'
    else:
        level = '安全'

    return total_score, level, '; '.join(reasons) if reasons else '正常'


def generate_predictions(weekly_data, predictor, max_devices=None):
    """
    生成生产部署版预测结果
    """
    results = []
    devices = weekly_data['所属设备'].unique()

    # 移除设备数量限制，预测所有设备
    if max_devices and max_devices > 0:
        devices = devices[:max_devices]  # 仅在明确指定时才限制
        print(f"开始预测 {len(devices)} 个设备（限制数量）...")
    else:
        print(f"开始预测所有 {len(devices)} 个设备...")

    for i, device in enumerate(devices):
        device_data = weekly_data[weekly_data['所属设备'] == device].sort_values('周')

        # 极简处理：实占率连续为0的设备
        if (device_data['实占率'] == 0).all():
            weeks_zero = len(device_data)
            latest_data = device_data.iloc[-1]
            result = {
                '设备': device,
                '数据变化类型': 'all_zero',
                '当前实占率百分比': "0.00%",
                '预测实占率百分比': "0.00%",
                '当前空闲端口数': safe_int(latest_data['空闲端口数']),
                '预测空闲端口数': 0,
                '当前潜在需求比百分比': "0.00%",
                '预测潜在需求比百分比': "0.00%",
                '预测风险评分': 0,
                '预测风险级别': '无风险',
                '预测风险原因': '设备无业务数据',
                '分光器容量': safe_int(latest_data['分光器容量']),
                '覆盖房间数': safe_int(latest_data['覆盖的工程级的线路到达房间数']),
                'FTTH终端数': safe_int(latest_data['ftth终端数']),
                '小区入库时间': latest_data['小区入库时间'],
                '入库时间差_月': round(latest_data['入库时间差_月'], 1) if pd.notna(latest_data['入库时间差_月']) else None,
                '备注': f"实占率连续{weeks_zero}周为0%，无业务趋势，预测结果恒为0%"
            }
            results.append(result)
            continue

        if i % 50 == 0:
            print(f"已处理 {i}/{len(devices)} 个设备")

        # 获取最新数据
        latest_data = device_data.iloc[-1]

        # 进行预测
        try:
            prediction = predictor.predict(device_data, device)
            device_type = predictor.device_types.get(device, 'unknown')

            # 根据设备类型确定风险处理策略
            if device_type in ['insufficient_data', 'basic_data']:
                # 对于数据不足或基础数据设备，使用特殊处理
                if device_type == 'insufficient_data':
                    risk_level = '数据不足'
                    risk_reasons = '历史数据严重不足，预测结果仅供参考，误差可能很大'
                    备注 = '数据点<5个，使用简单平均预测'
                else:  # basic_data
                    # 对基础数据设备也进行风险评估，但降低置信度
                    risk_score, risk_level, risk_reasons = calculate_risk_score(
                        prediction['occupancy'],
                        prediction['ports'],
                        prediction['demand'],
                        latest_data['分光器容量']
                    )
                    risk_reasons = f"数据量较少，预测精度有限。{risk_reasons}"
                    备注 = '数据点5-7个，使用基础趋势预测'

                result = {
                    '设备': device,
                    '数据变化类型': device_type,
                    '当前实占率百分比': f"{latest_data['实占率']*100:.2f}%",
                    '预测实占率百分比': f"{prediction['occupancy']*100:.2f}%",
                    '当前空闲端口数': safe_int(latest_data['空闲端口数']),
                    '预测空闲端口数': prediction['ports'],
                    '当前潜在需求比百分比': f"{latest_data['潜在需求比']*100:.2f}%",
                    '预测潜在需求比百分比': f"{prediction['demand']*100:.2f}%",
                    '预测风险评分': risk_score if device_type == 'basic_data' else 0,
                    '预测风险级别': risk_level,
                    '预测风险原因': risk_reasons,
                    '分光器容量': safe_int(latest_data['分光器容量']),
                    '覆盖房间数': safe_int(latest_data['覆盖的工程级的线路到达房间数']),
                    'FTTH终端数': safe_int(latest_data['ftth终端数']),
                    '小区入库时间': latest_data['小区入库时间'],
                    '入库时间差_月': round(latest_data['入库时间差_月'], 1) if pd.notna(latest_data['入库时间差_月']) else None,
                    '备注': 备注
                }
            else:
                # 正常设备的处理
                # 计算风险评分
                risk_score, risk_level, risk_reasons = calculate_risk_score(
                    prediction['occupancy'],
                    prediction['ports'],
                    prediction['demand'],
                    latest_data['分光器容量']
                )

                # 构建结果
                result = {
                    '设备': device,
                    '数据变化类型': predictor.device_types.get(device, 'unknown'),
                    '当前实占率百分比': f"{latest_data['实占率']*100:.2f}%",
                    '预测实占率百分比': f"{prediction['occupancy']*100:.2f}%",
                    '当前空闲端口数': safe_int(latest_data['空闲端口数']),
                    '预测空闲端口数': prediction['ports'],
                    '当前潜在需求比百分比': f"{latest_data['潜在需求比']*100:.2f}%",
                    '预测潜在需求比百分比': f"{prediction['demand']*100:.2f}%",
                    '预测风险评分': round(risk_score, 2),
                    '预测风险级别': risk_level,
                    '预测风险原因': risk_reasons,
                    '分光器容量': safe_int(latest_data['分光器容量']),
                    '覆盖房间数': safe_int(latest_data['覆盖的工程级的线路到达房间数']),
                    'FTTH终端数': safe_int(latest_data['ftth终端数']),
                    '小区入库时间': latest_data['小区入库时间'],
                    '入库时间差_月': round(latest_data['入库时间差_月'], 1) if pd.notna(latest_data['入库时间差_月']) else None
                }

            # 添加额外字段
            for col in ['建筑群', 'address_desc', 'area']:
                if col in latest_data:
                    result[col] = latest_data[col]

            results.append(result)

        except Exception as e:
            print(f"设备 {device} 预测失败: {e}")
            continue

    return results


def main():
    """主函数"""
    print("生产部署版混合模型预测系统启动...")

    # 设置路径
    data_folder = 'D:/OBD/data_processed'
    results_dir = 'HybridModel_Predictor_Modular_results'

    os.makedirs(results_dir, exist_ok=True) 

    # 检查数据文件夹
    if not os.path.exists(data_folder):
        print(f"错误：数据文件夹 {data_folder} 不存在")
        return

    try:
        # 1. 数据加载与预处理
        print("加载数据...")
        all_data = load_and_preprocess_data(data_folder)
        print(f"加载了 {len(all_data)} 条数据记录")

        # 2. 特征工程
        print("进行特征工程...")
        all_data = feature_engineering(all_data)

        # 3. 数据聚合
        print("按周聚合数据...")
        weekly_data = aggregate_weekly_data(all_data)
        print(f"共有 {len(weekly_data['所属设备'].unique())} 个设备的周度数据")

        # 4. 初始化生产部署版混合预测器
        print("初始化生产部署版混合预测器...")
        predictor = HybridPredictor()

        # 5. 生成预测结果（预测所有设备）
        print("生成预测结果...")
        results = generate_predictions(weekly_data, predictor)  # 移除设备数量限制

        # 6. 保存结果
        print("保存结果...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(results_dir, f'生产部署版混合模型预测结果_{timestamp}.xlsx')

        results_df = pd.DataFrame(results)
        results_df.to_excel(output_file, index=False)
        print(f'预测结果已保存至 {output_file}')

        # 7. 输出统计信息
        print(f"\n=== 生产部署版预测结果统计 ===")
        print(f"总预测设备数: {len(results)}")

        # 统计设备类型分布
        type_counts = results_df['数据变化类型'].value_counts()
        print("\n设备类型分布:")

        # 分层统计
        insufficient_count = type_counts.get('insufficient_data', 0)
        basic_count = type_counts.get('basic_data', 0)
        adequate_count = type_counts.get('adequate_data', 0)
        sufficient_count = len(results_df) - insufficient_count - basic_count - adequate_count

        print(f"  数据严重不足 (insufficient_data): {insufficient_count} 个设备 ({insufficient_count/len(results)*100:.1f}%) - <2个数据点")
        print(f"  数据极少 (basic_data): {basic_count} 个设备 ({basic_count/len(results)*100:.1f}%) - 2个数据点")
        print(f"  数据适中 (adequate_data): {adequate_count} 个设备 ({adequate_count/len(results)*100:.1f}%) - 3-7个数据点")
        print(f"  数据充足: {sufficient_count} 个设备 ({sufficient_count/len(results)*100:.1f}%) - ≥8个数据点")

        print("\n详细设备类型分布:")
        for device_type, count in type_counts.items():
            percentage = count / len(results) * 100
            print(f"  {device_type}: {count} 个设备 ({percentage:.1f}%)")

        # 统计风险级别分布
        risk_counts = results_df['预测风险级别'].value_counts()
        print("\n风险级别分布:")
        for level, count in risk_counts.items():
            percentage = count / len(results) * 100
            print(f"  {level}: {count} 个设备 ({percentage:.1f}%)")

        # 统计需要关注的设备
        high_risk = results_df[results_df['预测风险级别'].isin(['紧急', '警告'])]
        data_insufficient = results_df[results_df['预测风险级别'] == '数据不足']

        print(f"\n需要关注的设备数(紧急+警告): {len(high_risk)} 个设备")
        print(f"数据不足设备数: {len(data_insufficient)} 个设备")
        print(f"可靠预测设备数: {len(results) - len(data_insufficient)} 个设备")

        print("\n生产部署版混合模型预测完成!")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
