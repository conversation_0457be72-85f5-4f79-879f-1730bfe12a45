{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8789e20f-c809-406b-8d2a-9c95d514cf24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10加20等于多少？\n", "content='我来帮你计算10加20等于多少。' additional_kwargs={'tool_calls': [{'id': 'call_0_84a47fe6-3518-4f70-bff6-a3dcdd7dece0', 'function': {'arguments': '{\"a\": 10, \"b\": 20}', 'name': 'add'}, 'type': 'function', 'index': 0}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 34, 'prompt_tokens': 355, 'total_tokens': 389, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 355}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_Og1AzCtNZz_prod0718_fp8_kvcache', 'id': '9d7df5f4-6440-404a-9fbf-f6b88680ff25', 'finish_reason': 'tool_calls', 'logprobs': None} id='run--2b441ccb-7dd7-4a59-8c5c-c8187933f144-0' tool_calls=[{'name': 'add', 'args': {'a': 10, 'b': 20}, 'id': 'call_0_84a47fe6-3518-4f70-bff6-a3dcdd7dece0', 'type': 'tool_call'}] usage_metadata={'input_tokens': 355, 'output_tokens': 34, 'total_tokens': 389, 'input_token_details': {'cache_read': 0}, 'output_token_details': {}}\n", "-------------\n", "十的二十倍是多少？\n", "content='' additional_kwargs={'tool_calls': [{'id': 'call_0_02de9c88-7e37-41da-ad0d-0f62b370c85d', 'function': {'arguments': '{\"a\":10,\"b\":20}', 'name': 'multiply'}, 'type': 'function', 'index': 0}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 22, 'prompt_tokens': 355, 'total_tokens': 377, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 320}, 'prompt_cache_hit_tokens': 320, 'prompt_cache_miss_tokens': 35}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0623_fp8_kvcache', 'id': 'c6aa0121-68ef-4b32-b500-f24c3568a0ca', 'finish_reason': 'tool_calls', 'logprobs': None} id='run--81562438-f56a-423e-ad20-dc6d58d29c74-0' tool_calls=[{'name': 'multiply', 'args': {'a': 10, 'b': 20}, 'id': 'call_0_02de9c88-7e37-41da-ad0d-0f62b370c85d', 'type': 'tool_call'}] usage_metadata={'input_tokens': 355, 'output_tokens': 22, 'total_tokens': 377, 'input_token_details': {'cache_read': 320}, 'output_token_details': {}}\n", "-------------\n", "10的一半是多少\n", "content='' additional_kwargs={'tool_calls': [{'id': 'call_0_a6828558-9643-415d-8db4-04706015d504', 'function': {'arguments': '{\"a\":10,\"b\":2}', 'name': 'divide'}, 'type': 'function', 'index': 0}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 21, 'prompt_tokens': 352, 'total_tokens': 373, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 320}, 'prompt_cache_hit_tokens': 320, 'prompt_cache_miss_tokens': 32}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0623_fp8_kvcache', 'id': 'd0ab6114-eb19-49e0-af34-381c8cc6da2e', 'finish_reason': 'tool_calls', 'logprobs': None} id='run--a15fbe4b-5fe1-4f70-ade8-19fc9e6e50b7-0' tool_calls=[{'name': 'divide', 'args': {'a': 10, 'b': 2}, 'id': 'call_0_a6828558-9643-415d-8db4-04706015d504', 'type': 'tool_call'}] usage_metadata={'input_tokens': 352, 'output_tokens': 21, 'total_tokens': 373, 'input_token_details': {'cache_read': 320}, 'output_token_details': {}}\n", "-------------\n", "你的名字是什么？\n", "content='我是你的智能助手，你可以叫我助手或者AI助手！如果你愿意，也可以给我起个特别的名字哦！' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 23, 'prompt_tokens': 353, 'total_tokens': 376, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 320}, 'prompt_cache_hit_tokens': 320, 'prompt_cache_miss_tokens': 33}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0623_fp8_kvcache', 'id': '6a8509aa-86a8-4998-a7fc-a1ce44b85532', 'finish_reason': 'stop', 'logprobs': None} id='run--df5c79b3-23d6-4453-adfa-2c8746c8fa80-0' usage_metadata={'input_tokens': 353, 'output_tokens': 23, 'total_tokens': 376, 'input_token_details': {'cache_read': 320}, 'output_token_details': {}}\n", "-------------\n"]}], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "#创建一个和大模型的连接器\n", "url='https://api.deepseek.com'\n", "llm=ChatOpenAI(model='deepseek-chat',base_url=url, api_key=os.environ[\"DS_API_KEY\"])\n", "\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a * b\n", "\n", "# This will be a tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Adds a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a + b\n", "\n", "def divide(a: int, b: int) -> float:\n", "    \"\"\"Divide a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a / b\n", "\n", "tools = [add, multiply, divide]\n", "llm_with_tools = llm.bind_tools(tools)\n", "\n", "from langchain_core.messages import AIMessage, HumanMessage\n", "for x in ['10加20等于多少？','十的二十倍是多少？','10的一半是多少','你的名字是什么？']:\n", "    msgs=[HumanMessage(content=x,name=\"<PERSON>\")]\n", "    print(x)\n", "    print(llm_with_tools.invoke(msgs))\n", "    print('-------------')"]}, {"cell_type": "code", "execution_count": 2, "id": "8b06e085-ce7c-426b-b6d3-6c62deb7aca8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='我无法实时获取南京的天气信息，但你可以通过以下方式快速查询：  \\n\\n1. **天气预报应用**：如“中国天气网”、“墨迹天气”、“彩云天气”等。  \\n2. **搜索引擎**：在百度、谷歌等搜索“南京天气”即可看到实时数据。  \\n3. **手机自带天气功能**：大多数智能手机都有内置天气应用。  \\n\\n如果你需要未来几天的天气预报或穿衣建议，可以告诉我，我会根据常见气候特点提供参考！南京夏季炎热潮湿，冬季湿冷，春秋温和，近期注意是否有降雨或高温预警哦~' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 123, 'prompt_tokens': 8, 'total_tokens': 131, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 8}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0623_fp8_kvcache', 'id': '999e29b3-b17e-41b8-81ee-8ec0fa00bbea', 'finish_reason': 'stop', 'logprobs': None} id='run--2ea7c94e-e6df-4d53-80cd-4ca48daa2555-0' usage_metadata={'input_tokens': 8, 'output_tokens': 123, 'total_tokens': 131, 'input_token_details': {'cache_read': 0}, 'output_token_details': {}}\n"]}], "source": ["#对比没有工具绑定和有工具绑定的区别\n", "msgs=[HumanMessage(content='南京的天气是多少？',name=\"<PERSON>\")]\n", "res=llm.invoke(msgs) #没有工具绑定的LLM的回复\n", "print(res)"]}, {"cell_type": "code", "execution_count": 18, "id": "f83d808b-64b2-4caf-9db1-6a6d03a50f26", "metadata": {}, "outputs": [], "source": ["#绑定自己设计的天气工具\n", "def search_weather(location):\n", "    '''这是一个天气查询的工具。输入的参数是 location: 表示要查询的城市'''\n", "    #真正的访问天气查询的API，获得返回值。\n", "    return location + '的天气非常好，适合外出。'\n", "def search_phone_cost(phone_number):\n", "    '''这是一个手机话费查询工具，输入的参数是 phone_number， 表示要查询的手机号码。 '''\n", "    return phone_number+'已欠费'\n", "def search_lucky(person_name):\n", "    '''这是一个今日运势查询的工具，输入的参数是person_name，表示要查询的当事人。'''\n", "    return person_name+'今日必能暴富'"]}, {"cell_type": "code", "execution_count": 19, "id": "00b30e43-77a3-41e5-8824-981d7849bdbb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='我来帮您查询手机号18611112222的话费情况。' additional_kwargs={'tool_calls': [{'id': 'call_0_7ee18247-01cb-4adb-82f2-b4022a1a391c', 'function': {'arguments': '{\"phone_number\": 18611112222}', 'name': 'search_phone_cost'}, 'type': 'function', 'index': 0}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 40, 'prompt_tokens': 291, 'total_tokens': 331, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 291}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_Og1AzCtNZz_prod0718_fp8_kvcache', 'id': '386eae67-0526-4eae-b996-fbe2d4af7274', 'finish_reason': 'tool_calls', 'logprobs': None} id='run--4c85630e-af52-42ef-b897-57245385e8f0-0' tool_calls=[{'name': 'search_phone_cost', 'args': {'phone_number': 18611112222}, 'id': 'call_0_7ee18247-01cb-4adb-82f2-b4022a1a391c', 'type': 'tool_call'}] usage_metadata={'input_tokens': 291, 'output_tokens': 40, 'total_tokens': 331, 'input_token_details': {'cache_read': 0}, 'output_token_details': {}}\n"]}], "source": ["llm_with_tools = llm.bind_tools([search_weather,search_phone_cost,search_lucky])\n", "msgs=[HumanMessage(content='帮我查查手机花费，我的手机号 18611112222',name=\"张老师\")]\n", "res=llm_with_tools.invoke(msgs)\n", "print(res)"]}, {"cell_type": "code", "execution_count": 20, "id": "9be347c6-248b-459d-8d65-82c290be6f2b", "metadata": {}, "outputs": [], "source": ["#完整的工具调用代码\n", "from langgraph.graph import START, StateGraph, END\n", "from langgraph.prebuilt import tools_condition\n", "from langgraph.prebuilt import ToolNode\n", "from IPython.display import Image, display\n", "from langgraph.graph import MessagesState\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "#构建Graph的代码\n", "# System message\n", "sys_msg = SystemMessage(content=\"You are a helpful assistant tasked with performing arithmetic on a set of inputs.\")\n", "\n", "# Node\n", "def assistant(state: MessagesState):\n", "   return {\"messages\": [llm_with_tools.invoke([sys_msg] + state[\"messages\"])]}\n", "\n", "# Graph\n", "builder = StateGraph(MessagesState)\n", "\n", "# Define nodes: these do the work\n", "builder.add_node(\"assistant\", assistant)\n", "builder.add_node(\"tools\", ToolNode([search_weather,search_phone_cost,search_lucky]))\n", "\n", "# Define edges: these determine how the control flow moves\n", "builder.add_edge(START, \"assistant\")\n", "builder.add_conditional_edges(\n", "    \"assistant\",\n", "    tools_condition,\n", ")\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 21, "id": "6c826880-baf6-4ec6-821f-67d6cb5ef846", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["白岩松今日必能暴富\n"]}], "source": ["msgs=[HumanMessage(content='帮白岩松查查它最近的运势怎么样？',name=\"张老师\")]\n", "res=graph.invoke({\"messages\": msgs})\n", "print(res['messages'][-1].content)"]}, {"cell_type": "code", "execution_count": 22, "id": "978bb19f-1414-43ef-9309-572240c8f395", "metadata": {}, "outputs": [], "source": ["#反思的智能体\n", "#完整的工具调用代码\n", "from langgraph.graph import START, StateGraph, END\n", "from langgraph.prebuilt import tools_condition\n", "from langgraph.prebuilt import ToolNode\n", "from IPython.display import Image, display\n", "from langgraph.graph import MessagesState\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "#构建Graph的代码\n", "# System message\n", "sys_msg = SystemMessage(content=\"You are a helpful assistant tasked with performing arithmetic on a set of inputs.\")\n", "\n", "# Node\n", "def assistant(state: MessagesState):\n", "   return {\"messages\": [llm_with_tools.invoke([sys_msg] + state[\"messages\"])]}\n", "\n", "# Graph\n", "builder = StateGraph(MessagesState)\n", "\n", "# Define nodes: these do the work\n", "builder.add_node(\"assistant\", assistant)\n", "builder.add_node(\"tools\", ToolNode([search_weather,search_phone_cost,search_lucky]))\n", "\n", "# Define edges: these determine how the control flow moves\n", "builder.add_edge(START, \"assistant\")\n", "builder.add_conditional_edges(\n", "    \"assistant\",\n", "    tools_condition,\n", ")\n", "builder.add_edge('tools', \"assistant\")\n", "\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 25, "id": "a74ffe04-c7e0-4837-9c21-b8609295bfd0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 白岩松的手机话费已欠费。\n", "2. 白岩松今日运势非常好，必能暴富！\n", "3. 南京的天气非常好，适合外出。你可以放心去南京找他！\n"]}], "source": ["msgs=[HumanMessage(content='查查白岩松的手机话费，然后再查查它最近的运势怎么样？我想去南京找他，南京的天气怎么样？',name=\"张老师\")]\n", "res=graph.invoke({\"messages\": msgs})\n", "print(res['messages'][-1].content)"]}, {"cell_type": "code", "execution_count": null, "id": "b7b24cf8-9709-4ced-8c5b-701216020aef", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}