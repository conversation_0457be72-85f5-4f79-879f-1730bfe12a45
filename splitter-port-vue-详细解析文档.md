# splitter-port.vue 文件详细解析文档

## 文件概述

`splitter-port.vue` 是一个 Vue 3 单文件组件，实现了分光器端口预警管理系统的核心功能。该文件采用 Composition API + TypeScript 的开发模式，包含数据展示、图表可视化、预测分析等功能模块。

## 🔴 重要说明：演示数据与后端对接

本文档中所有标注为 **🎭 演示数据** 的内容都是前端模拟的假数据，在实际项目中需要：
1. **后端 Controller** 提供对应的 REST API 接口
2. **后端 Service** 实现业务逻辑处理
3. **后端 DAO/Repository** 进行数据库操作
4. **前端 API 调用** 替换所有 mock 方法

标注说明：
- **🎭 演示数据** - 需要替换为真实 API 调用的模拟数据
- **🔌 后端对接** - 需要后端提供接口支持的功能
- **📊 计算逻辑** - 可在前端计算或后端计算的业务逻辑

## 📋 文档目录

### 第一部分：Template 模板结构详解
- 1.1 根容器和页面头部
- 1.2 统计卡片区域
- 1.3 趋势图可视化区域
- 1.4 搜索筛选区域
- 1.5 设备容器数据表格
- 1.6 分光器详情弹窗
- 1.7 预测分析弹窗
- 1.8 设备预测详情弹窗

### 第二部分：Script 逻辑代码详解
- 2.1 导入依赖和组件定义
- 2.2 响应式数据定义
- 2.3 统计数据和配置对象
- 2.4 数据存储变量
- 2.5 TypeScript 接口定义
- 2.6 表格列配置
- 2.7 基础方法定义
- 2.8 设备详情和预警处理
- 2.9 预测分析相关方法
- 2.10 批量预测和设备预测详情
- 2.11 趋势图相关方法
- 2.12 工具方法集合
- 2.13 预测状态相关方法
- 2.14 数据加载方法
- 2.15 图表初始化
- 2.16 趋势数据加载和图表初始化
- 2.17 设备预测图表初始化
- 2.18 单设备预测图表初始化
- 2.19 生命周期钩子

### 第三部分：Style 样式代码详解
- 3.1 页面头部样式
- 3.2 统计卡片样式
- 3.3 表格和操作按钮样式
- 3.4 响应式设计

### 第四部分：演示数据与后端对接详细说明
- 4.1 所有演示数据汇总
- 4.2 后端接口需求详细说明
- 4.3 数据库表结构建议

### 第五部分：模块间相互作用分析
- 5.1 数据流向图
- 5.2 核心交互流程
- 5.3 状态管理模式
- 5.4 组件通信机制
- 5.5 性能优化策略

### 第六部分：总结

### 第七部分：模拟数据方法详细说明
- 7.1 所有 Mock 方法汇总
- 7.2 API 调用替换指南
- 7.3 替换示例

## 文件结构

Vue 单文件组件由三个主要部分组成：
- `<template>` - HTML 模板，定义页面结构
- `<script setup>` - JavaScript/TypeScript 逻辑，定义数据和方法
- `<style>` - CSS 样式，定义页面外观

---

## 第一部分：Template 模板结构详解

### 1.1 根容器和页面头部

```vue
<template>
  <PageWrapper>
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button type="text" @click="goBack" class="back-btn">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
        <h1 class="page-title">分光器端口预警</h1>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="refreshData" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
      </div>
    </div>
```

**代码解释：**
- `<PageWrapper>` - 自定义页面包装组件，提供统一的页面布局
- `class="page-header"` - 页面头部容器，使用 flexbox 布局
- `@click="goBack"` - 点击事件绑定，调用 goBack 方法返回上一页
- `:loading="loading"` - 动态绑定 loading 状态，控制按钮加载动画
- `<template #icon>` - Vue 3 插槽语法，定义按钮图标内容

### 1.2 统计卡片区域

```vue
    <!-- 统计卡片区域 -->
    <div class="statistics-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card normal">
            <a-statistic
              title="正常设备"
              :value="statistics.normalDevices"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card attention">
            <a-statistic
              title="注意设备"
              :value="statistics.attentionDevices"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card alarm">
            <a-statistic
              title="告警设备"
              :value="statistics.alarmDevices"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <CloseCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card total">
            <a-statistic
              title="总设备数"
              :value="statistics.totalDevices"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <DatabaseOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
      <div class="update-time">
        <ClockCircleOutlined />
        最近更新时间：{{ statistics.lastUpdateTime }}
      </div>
    </div>
```

**代码解释：**
- `<a-row :gutter="16">` - Ant Design 栅格系统，设置列间距为 16px
- `:span="6"` - 每列占 6/24 的宽度，即 25%
- `:value="statistics.normalDevices"` - 动态绑定统计数据 **🎭 演示数据**
- `:value-style="{ color: '#52c41a' }"` - 设置数值颜色样式
- `{{ statistics.lastUpdateTime }}` - 插值表达式，显示最后更新时间 **🎭 演示数据**

**🔌 后端对接需求：**
- 需要后端提供统计数据接口：`GET /api/splitter/statistics`
- 返回数据格式：`{ normalDevices: number, attentionDevices: number, alarmDevices: number, totalDevices: number, lastUpdateTime: string }`

### 1.3 趋势图可视化区域

```vue
    <!-- 趋势图可视化区域 -->
    <a-card class="trend-chart-card">
      <template #title>
        <div class="trend-chart-header">
          <span>实占率趋势图</span>
          <a-button
            type="text"
            size="small"
            @click="toggleTrendChart"
            class="collapse-btn"
          >
            <template #icon>
              <UpOutlined v-if="trendChartCollapsed" />
              <DownOutlined v-else />
            </template>
            {{ trendChartCollapsed ? '展开' : '收起' }}
          </a-button>
        </div>
      </template>

      <div v-show="!trendChartCollapsed" class="trend-chart-content">
        <!-- 趋势图配置区域 -->
        <div class="trend-config">
          <a-form layout="inline" :model="trendConfig">
            <a-form-item label="设备/分光器编码">
              <a-input-search
                v-model:value="trendConfig.searchCode"
                placeholder="请输入设备编码或分光器编码"
                style="width: 300px"
                enter-button="搜索"
                @search="onSearchTrend"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="时间范围">
              <a-radio-group v-model:value="trendConfig.timeRange" @change="onTimeRangeChange">
                <a-radio-button value="month">最近30天</a-radio-button>
                <a-radio-button value="quarter">最近90天</a-radio-button>
              </a-radio-group>
            </a-form-item>
            <a-form-item>
              <a-button @click="clearTrendSelection" size="small">
                清空
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <!-- 图表展示区域 -->
        <div class="chart-display">
          <div v-if="trendConfig.currentDevice" class="chart-container">
            <div class="chart-header">
              <span class="device-info">{{ trendConfig.currentDevice }} 实占率趋势</span>
            </div>
            <div ref="trendChartRef" style="width: 100%; height: 350px;"></div>
          </div>
          <div v-else class="empty-chart">
            <a-empty description="请输入设备编码或分光器编码查看趋势图" />
          </div>
        </div>
      </div>
    </a-card>
```

**代码解释：**
- `<template #title>` - 卡片标题插槽，自定义标题内容
- `v-show="!trendChartCollapsed"` - 条件显示，控制图表区域的展开/收起
- `v-model:value="trendConfig.searchCode"` - 双向数据绑定，绑定搜索输入框
- `@search="onSearchTrend"` - 搜索事件绑定 **🔌 后端对接**
- `ref="trendChartRef"` - 模板引用，用于获取 DOM 元素进行图表渲染
- `v-if="trendConfig.currentDevice"` - 条件渲染，有设备时显示图表，否则显示空状态

**🔌 后端对接需求：**
- 需要后端提供趋势数据接口：`GET /api/splitter/trend?deviceCode={code}&timeRange={range}`
- 支持设备编码搜索和时间范围筛选

### 1.4 搜索筛选区域

```vue
    <!-- 搜索筛选区域 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filters" @finish="handleSearch">
        <a-form-item label="设备编码">
          <a-input
            v-model:value="filters.deviceCode"
            placeholder="请输入设备编码"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="区域">
          <a-select
            v-model:value="filters.region"
            placeholder="请选择区域"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="常州">常州</a-select-option> <!-- 🎭 演示数据 -->
            <a-select-option value="无锡">无锡</a-select-option> <!-- 🎭 演示数据 -->
            <a-select-option value="苏州">苏州</a-select-option> <!-- 🎭 演示数据 -->
          </a-select>
        </a-form-item>
        <a-form-item label="设备状态">
          <a-select
            v-model:value="filters.deviceStatus"
            placeholder="请选择设备状态"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="normal">正常</a-select-option>
            <a-select-option value="attention">注意</a-select-option>
            <a-select-option value="alarm">告警</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="日期范围">
          <a-range-picker
            v-model:value="filters.dateRange"
            style="width: 240px"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">重置</a-button>
            <a-button type="primary" ghost @click="showPredictionModal">
              <template #icon>
                <LineChartOutlined />
              </template>
              单设备预测
            </a-button>
            <a-button type="primary" @click="executeAllPrediction" :loading="allPredictionLoading">
              <template #icon>
                <ThunderboltOutlined />
              </template>
              全部预测
            </a-button>
            <a-button @click="handleExport">
              <template #icon>
                <ExportOutlined />
              </template>
              导出数据
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
```

**代码解释：**
- `:model="filters"` - 表单数据绑定，绑定筛选条件对象
- `@finish="handleSearch"` - 表单提交事件，触发搜索功能 **🔌 后端对接**
- `html-type="submit"` - 设置按钮为提交类型
- `type="primary" ghost` - 设置按钮样式为主要颜色的幽灵按钮
- `<a-space>` - 间距组件，自动设置子元素间距

**🔌 后端对接需求：**
- 搜索接口：`POST /api/splitter/search`
- 全部预测接口：`POST /api/splitter/predict/batch`
- 导出接口：`GET /api/splitter/export`
- 区域数据需要从后端动态获取：`GET /api/regions`

### 1.5 设备容器数据表格

```vue
    <!-- 设备容器数据表格 -->
    <a-card class="table-card">
      <a-table
        :columns="columns"
        :data-source="deviceList"
        :pagination="pagination"
        :loading="loading"
        row-key="设备编码"
        :scroll="{ x: 1800, y: 500 }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === '实占率'">
            <div class="occupancy-cell">
              <a-progress
                :percent="record.实占率"
                :stroke-color="getProgressColor(record.实占率)"
                :show-info="false"
                size="small"
              />
              <span class="occupancy-text">{{ record.实占率 }}%</span>
            </div>
          </template>
          <template v-if="column.key === '健康状态'">
            <a-tag :color="getStatusColor(record.健康状态)">
              {{ getStatusText(record.健康状态) }}
            </a-tag>
          </template>
          <template v-if="column.key === '预测实占率'">
            <div v-if="record.预测实占率 !== undefined" class="occupancy-cell">
              <a-progress
                :percent="record.预测实占率"
                :stroke-color="getProgressColor(record.预测实占率)"
                :show-info="false"
                size="small"
              />
              <span class="occupancy-text">{{ record.预测实占率 }}%</span>
            </div>
            <span v-else class="no-prediction">未预测</span>
          </template>
          <template v-if="column.key === '预测空闲数'">
            <span v-if="record.预测空闲数 !== undefined">
              {{ record.预测空闲数 }}
            </span>
            <span v-else class="no-prediction">-</span>
          </template>
          <template v-if="column.key === '预测状态'">
            <a-tag v-if="record.预测状态" :color="getPredictionStatusColor(record.预测状态)">
              {{ getPredictionStatusText(record.预测状态) }}
            </a-tag>
            <span v-else class="no-prediction">-</span>
          </template>
          <template v-if="column.key === 'action'">
            <div class="action-buttons">
              <a-button type="link" size="small" @click="showDeviceDetail(record)" class="action-btn">
                详情
              </a-button>
              <a-button type="link" size="small" @click="showDevicePrediction(record)" class="action-btn">
                预测
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                v-if="record.健康状态 !== 'normal'"
                @click="handleWarning(record)"
                class="action-btn"
              >
                处理
              </a-button>
              <!-- 占位元素，确保对齐 -->
              <span v-else class="action-btn-placeholder"></span>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>
```

**代码解释：**
- `:columns="columns"` - 绑定表格列配置
- `:data-source="deviceList"` - 绑定表格数据源 **🎭 演示数据**
- `:pagination="pagination"` - 绑定分页配置
- `row-key="设备编码"` - 设置行的唯一标识
- `:scroll="{ x: 1800, y: 500 }"` - 设置表格滚动区域
- `<template #bodyCell="{ column, record }">` - 自定义单元格渲染
- `v-if="column.key === '实占率'"` - 根据列键值条件渲染不同内容
- `:percent="record.实占率"` - 进度条百分比绑定 **📊 计算逻辑**
- `:stroke-color="getProgressColor(record.实占率)"` - 动态设置进度条颜色 **📊 计算逻辑**
- `@click="showDeviceDetail(record)"` - 点击事件，传递当前行数据 **🔌 后端对接**

**🔌 后端对接需求：**
- 设备列表接口：`GET /api/splitter/devices?page={page}&size={size}&filters={filters}`
- 设备详情接口：`GET /api/splitter/device/{deviceCode}/details`
- 设备预测接口：`POST /api/splitter/device/{deviceCode}/predict`

### 1.6 分光器详情弹窗

```vue
    <!-- 分光器详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="分光器详情"
      width="1200px"
      :footer="null"
    >
      <a-table
        :columns="detailColumns"
        :data-source="splitterDetails"
        :pagination="false"
        size="small"
        row-key="splitterCode"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === '实占率'">
            <div class="occupancy-cell">
              <a-progress
                :percent="record.实占率"
                :stroke-color="getProgressColor(record.实占率)"
                :show-info="false"
                size="small"
              />
              <span class="occupancy-text">{{ record.实占率 }}%</span>
            </div>
          </template>
          <template v-if="column.key === '健康状态'">
            <a-tag :color="getStatusColor(record.健康状态)">
              {{ getStatusText(record.健康状态) }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
```

**代码解释：**
- `v-model:open="detailModalVisible"` - 双向绑定模态框显示状态
- `width="1200px"` - 设置模态框宽度
- `:footer="null"` - 隐藏模态框底部按钮
- `:pagination="false"` - 禁用表格分页
- `size="small"` - 设置表格为小尺寸

**🔌 后端对接需求：**
- 分光器详情接口：`GET /api/splitter/device/{deviceCode}/splitters`
- 返回该设备下所有分光器的详细信息

### 1.7 预测分析弹窗

```vue
    <!-- 预测分析弹窗 -->
    <a-modal
      v-model:open="predictionModalVisible"
      title="预测分析"
      width="1200px"
      :footer="null"
    >
      <div class="prediction-content">
        <!-- 预测配置区域 -->
        <div class="prediction-config">
          <a-form layout="inline" :model="predictionConfig">
            <a-form-item label="设备编码">
              <a-input
                v-model:value="predictionConfig.selectedDevice"
                placeholder="请输入设备编码"
                style="width: 200px"
                allow-clear
              />
            </a-form-item>
            <a-form-item label="预测周期">
              <a-radio-group v-model:value="predictionConfig.period" @change="onPredictionPeriodChange">
                <a-radio-button value="week">一周</a-radio-button>
                <a-radio-button value="month">一个月</a-radio-button>
              </a-radio-group>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="executePrediction" :loading="predictionLoading">
                <template #icon>
                  <LineChartOutlined />
                </template>
                执行预测
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <!-- 图表展示区域 -->
        <div class="chart-container" v-if="predictionData.hasData">
          <div ref="chartRef" style="width: 100%; height: 400px;"></div>
        </div>

        <!-- 预测结果展示 -->
        <div class="prediction-info" v-if="predictionData.hasData">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="预测结果" size="small">
                <p>设备编码：<span class="highlight">{{ predictionData.设备编码 }}</span></p>
                <p v-if="predictionConfig.period === 'week'">下周预测占用率：<span class="highlight">{{ predictionData.prediction?.nextWeekRate }}%</span></p>
                <p v-if="predictionConfig.period === 'month'">下月预测占用率：<span class="highlight">{{ predictionData.prediction?.nextMonthRate }}%</span></p>
                <p>趋势：<a-tag :color="getTrendColor(predictionData.prediction?.trend)">{{ getTrendText(predictionData.prediction?.trend) }}</a-tag></p>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="建议方案" size="small">
                <p><strong>扩容建议：</strong>{{ predictionData.recommendations?.expansionAdvice }}</p>
                <p><strong>调整建议：</strong>{{ predictionData.recommendations?.adjustmentAdvice }}</p>
                <p><strong>优先级：</strong><a-tag :color="getPriorityColor(predictionData.recommendations?.priority)">{{ getPriorityText(predictionData.recommendations?.priority) }}</a-tag></p>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-if="!predictionData.hasData">
          <a-empty description="请配置预测参数并执行预测分析" />
        </div>
      </div>
    </a-modal>
```

**代码解释：**
- `@change="onPredictionPeriodChange"` - 预测周期变化事件
- `ref="chartRef"` - 图表容器的模板引用
- `v-if="predictionData.hasData"` - 条件渲染，有数据时显示图表和结果
- `{{ predictionData.prediction?.nextWeekRate }}` - 可选链操作符，安全访问嵌套属性 **🎭 演示数据**
- `:color="getTrendColor(predictionData.prediction?.trend)"` - 动态设置标签颜色 **📊 计算逻辑**

**🔌 后端对接需求：**
- 单设备预测接口：`POST /api/splitter/predict/single`
- 请求参数：`{ deviceCode: string, period: 'week' | 'month' }`
- 返回预测结果和建议方案

### 1.8 设备预测详情弹窗

```vue
    <!-- 设备预测详情弹窗 -->
    <a-modal
      v-model:open="devicePredictionModalVisible"
      title="设备预测详情"
      width="1000px"
      :footer="null"
      @cancel="handleDevicePredictionModalClose"
    >
      <div class="device-prediction-content">
        <!-- 设备基本信息 -->
        <div class="device-info-header">
          <a-descriptions :column="3" size="small">
            <a-descriptions-item label="设备编码">{{ selectedDeviceForPrediction?.设备编码 }}</a-descriptions-item>
            <a-descriptions-item label="区域">{{ selectedDeviceForPrediction?.区域 }}</a-descriptions-item>
            <a-descriptions-item label="当前实占率">{{ selectedDeviceForPrediction?.实占率 }}%</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 趋势图表 -->
        <div class="prediction-chart-container">
          <div v-if="devicePredictionData.hasData" ref="devicePredictionChartRef" style="width: 100%; height: 350px;"></div>
          <div v-else class="chart-loading">
            <a-spin size="large" />
            <p style="margin-top: 16px; text-align: center;">正在加载图表数据...</p>
          </div>
        </div>

        <!-- 预测结果详情 -->
        <div class="device-prediction-info" v-if="devicePredictionData.hasData">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card title="预测结果" size="small">
                <p>下周预测占用率：<span class="highlight">{{ devicePredictionData.prediction?.nextWeekRate }}%</span></p>
                <p>下月预测占用率：<span class="highlight">{{ devicePredictionData.prediction?.nextMonthRate }}%</span></p>
                <p>趋势：<a-tag :color="getTrendColor(devicePredictionData.prediction?.trend)">{{ getTrendText(devicePredictionData.prediction?.trend) }}</a-tag></p>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card title="风险评估" size="small">
                <p>风险等级：<a-tag :color="getRiskLevelColor(devicePredictionData.riskAssessment?.level)">{{ getRiskLevelText(devicePredictionData.riskAssessment?.level) }}</a-tag></p>
                <p>风险评分：<span class="highlight">{{ devicePredictionData.riskAssessment?.score }}</span></p>
                <p>置信度：<span class="highlight">{{ devicePredictionData.businessInsights?.confidenceLevel }}%</span></p>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card title="建议方案" size="small">
                <p><strong>扩容建议：</strong>{{ devicePredictionData.recommendations?.expansionAdvice }}</p>
                <p><strong>调整建议：</strong>{{ devicePredictionData.recommendations?.adjustmentAdvice }}</p>
                <p><strong>优先级：</strong><a-tag :color="getPriorityColor(devicePredictionData.recommendations?.priority)">{{ getPriorityText(devicePredictionData.recommendations?.priority) }}</a-tag></p>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 加载状态 -->
        <div v-if="!devicePredictionData.hasData" class="loading-state">
          <a-spin size="large" />
          <p style="margin-top: 16px; text-align: center;">正在加载预测数据...</p>
        </div>
      </div>
    </a-modal>
  </PageWrapper>
</template>
```

**代码解释：**
- `@cancel="handleDevicePredictionModalClose"` - 模态框关闭事件
- `<a-descriptions>` - 描述列表组件，展示设备基本信息
- `:column="3"` - 设置描述列表为3列布局
- `ref="devicePredictionChartRef"` - 设备预测图表的模板引用
- `<a-spin size="large" />` - 加载动画组件
- `:span="8"` - 每列占8/24的宽度，即33.33%

---

## 第二部分：Script 逻辑代码详解

### 2.1 导入依赖和组件定义

```typescript
<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { PageWrapper } from '@/components/Page';
import { useECharts } from '@/hooks/web/useECharts';
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  LineChartOutlined,
  ExportOutlined,
  UpOutlined,
  DownOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';

defineOptions({
  name: 'SplitterPortWarning'
});
```

**代码解释：**
- `<script setup lang="ts">` - 使用 Vue 3 Composition API 的 setup 语法糖，启用 TypeScript
- `import { ref, reactive, onMounted, nextTick } from 'vue'` - 导入 Vue 3 响应式 API
- `useRouter` - Vue Router 的路由钩子，用于页面导航
- `message` - Ant Design Vue 的消息提示组件
- `useECharts` - 自定义 Hook，用于 ECharts 图表初始化
- 图标导入 - 从 Ant Design Icons 导入所需的图标组件
- `defineOptions` - 定义组件选项，设置组件名称

### 2.2 响应式数据定义

```typescript
// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const detailModalVisible = ref(false);
const predictionModalVisible = ref(false);
const predictionLoading = ref(false);
const allPredictionLoading = ref(false);
const devicePredictionModalVisible = ref(false);
const chartRef = ref<HTMLDivElement>();
const trendChartRef = ref<HTMLDivElement>();
const devicePredictionChartRef = ref<HTMLDivElement>();
const trendChartCollapsed = ref(false);
```

**代码解释：**
- `const router = useRouter()` - 获取路由实例，用于页面跳转
- `ref(false)` - 创建响应式引用，用于存储布尔值状态
- `ref<HTMLDivElement>()` - 创建 DOM 元素引用，用于获取模板中的元素
- 各种 loading 状态用于控制按钮和页面的加载状态
- 各种 modal 状态用于控制弹窗的显示/隐藏
- 图表引用用于 ECharts 实例的 DOM 挂载

### 2.3 统计数据和配置对象

```typescript
// 统计数据 🎭 演示数据
const statistics = reactive({
  normalDevices: 156,        // 🎭 演示数据 - 需要从后端获取
  attentionDevices: 23,      // 🎭 演示数据 - 需要从后端获取
  alarmDevices: 5,           // 🎭 演示数据 - 需要从后端获取
  totalDevices: 184,         // 🎭 演示数据 - 需要从后端获取
  lastUpdateTime: '2024-01-15 14:30:25'  // 🎭 演示数据 - 需要从后端获取
});

// 筛选条件
const filters = reactive({
  deviceCode: '',
  region: '',
  deviceStatus: '',
  dateRange: [] as any[]
});

// 预测配置
const predictionConfig = reactive({
  selectedDevice: '',
  period: 'week' as 'week' | 'month'
});

// 趋势图配置
const trendConfig = reactive({
  searchCode: '',
  currentDevice: '',
  timeRange: 'month' as 'month' | 'quarter'
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});
```

**代码解释：**
- `reactive()` - 创建响应式对象，用于存储复杂的数据结构
- `statistics` - 存储页面顶部统计卡片的数据 **🎭 演示数据**
- `filters` - 存储搜索筛选条件
- `predictionConfig` - 存储预测分析的配置参数
- `trendConfig` - 存储趋势图的配置参数
- `pagination` - 存储表格分页的配置
- `as 'week' | 'month'` - TypeScript 类型断言，限制值的类型
- `showTotal: (total: number) => string` - 函数类型，用于自定义分页信息显示

**🔌 后端对接说明：**
- `statistics` 对象中的所有数据都需要通过 API 从后端获取
- `filters` 中的筛选条件会作为查询参数发送给后端
- `pagination` 配置用于前后端分页数据交互

### 2.4 数据存储变量

```typescript
// 设备列表数据
const deviceList = ref<DeviceContainer[]>([]);

// 分光器详情数据
const splitterDetails = ref<SplitterDetail[]>([]);

// 预测分析数据
const predictionData = ref<PredictionAnalysis>({
  hasData: false
} as PredictionAnalysis);

// 设备预测详情数据
const selectedDeviceForPrediction = ref<DeviceContainer | null>(null);
const devicePredictionData = ref<DevicePredictionAnalysis>({
  hasData: false
} as DevicePredictionAnalysis);
```

**代码解释：**
- `ref<DeviceContainer[]>([])` - 创建类型化的响应式数组引用 **🎭 演示数据**
- `DeviceContainer` - 自定义 TypeScript 接口，定义设备数据结构
- `as PredictionAnalysis` - 类型断言，确保对象符合接口定义
- `null` - 初始值为空，表示未选择设备

**🔌 后端对接说明：**
- `deviceList` - 设备列表数据，需要从后端 API 获取
- `splitterDetails` - 分光器详情数据，需要根据设备编码从后端获取
- `predictionData` 和 `devicePredictionData` - 预测分析结果，需要后端算法支持

### 2.5 TypeScript 接口定义

```typescript
// 数据类型定义
interface DeviceContainer {
  设备编码: string;
  区域: string;
  小区入库时间: string;
  覆盖的工程级的线路到达房间数: number;
  分光器数: number;
  分光器容量: number;
  分光器空闲数: number;
  ftth终端数: number;
  // 计算字段
  实占率: number;
  健康状态: 'normal' | 'attention' | 'alarm';
  // 预测字段
  预测实占率?: number;
  预测空闲数?: number;
  预测状态?: 'normal' | 'attention' | 'alarm' | 'expansion';
}

interface SplitterDetail {
  分光器编码: string;
  安装地址: string;
  分光器容量: number;
  分光器空闲数: number;
  实占率: number;
  入网时间: string;
  数据更新时间: string;
  健康状态: 'normal' | 'attention' | 'alarm';
}

interface PredictionAnalysis {
  hasData: boolean;
  设备编码?: string;
  historicalData?: Array<{
    week: string;
    occupancyRate: number;
  }>;
  prediction?: {
    nextWeekRate: number;
    nextMonthRate: number;
    trend: 'increasing' | 'stable' | 'decreasing';
  };
  batchPrediction?: {
    avgNextWeekRate: number;
    avgNextMonthRate: number;
    highRiskCount: number;
  };
  overallPrediction?: {
    avgOccupancyRate: number;
    attentionDeviceCount: number;
    alarmDeviceCount: number;
  };
  recommendations?: {
    expansionAdvice: string;
    adjustmentAdvice: string;
    priority: 'high' | 'medium' | 'low';
  };
}

interface DevicePredictionAnalysis {
  hasData: boolean;
  设备编码?: string;
  historicalData?: Array<{
    week: string;
    occupancyRate: number;
  }>;
  prediction?: {
    nextWeekRate: number;
    nextMonthRate: number;
    trend: 'increasing' | 'stable' | 'decreasing';
  };
  riskAssessment?: {
    score: number;
    level: 'low' | 'medium' | 'high';
    reasons: string[];
  };
  businessInsights?: {
    trendAnalysis: string;
    expansionAdvice: string;
    confidenceLevel: number;
  };
  recommendations?: {
    expansionAdvice: string;
    adjustmentAdvice: string;
    priority: 'high' | 'medium' | 'low';
  };
}
```

**代码解释：**
- `interface` - TypeScript 接口定义，用于类型约束
- `string | number` - 联合类型，表示可以是字符串或数字
- `'normal' | 'attention' | 'alarm'` - 字面量类型，限制值只能是这几个选项
- `?:` - 可选属性，表示该属性可能不存在
- `Array<{}>` - 数组类型，包含特定结构的对象
- 接口定义了数据的结构，确保类型安全和代码提示

**🔌 后端对接说明：**
- 这些接口定义了前后端数据交互的标准格式
- 后端返回的 JSON 数据必须符合这些接口定义
- 建议后端也使用相同的数据结构定义（如 Java 的实体类、DTO 等）

### 2.6 表格列配置

```typescript
// 表格列定义
const columns: TableColumnsType = [
  {
    title: '设备编码',
    dataIndex: '设备编码',
    key: '设备编码',
    width: 180,
    fixed: 'left'
  },
  {
    title: '区域',
    dataIndex: '区域',
    key: '区域',
    width: 100,
    fixed: 'left'
  },
  {
    title: '实占率',
    dataIndex: '实占率',
    key: '实占率',
    width: 120,
    align: 'center'
  },
  {
    title: '健康状态',
    dataIndex: '健康状态',
    key: '健康状态',
    width: 100,
    align: 'center'
  },
  // ... 更多列配置
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
    align: 'left'
  }
];

// 分光器详情表格列定义
const detailColumns: TableColumnsType = [
  {
    title: '分光器编码',
    dataIndex: '分光器编码',
    key: '分光器编码',
    width: 150
  },
  // ... 更多列配置
];
```

**代码解释：**
- `TableColumnsType` - Ant Design Vue 表格列的类型定义
- `title` - 列标题显示文本
- `dataIndex` - 对应数据对象的属性名
- `key` - 列的唯一标识
- `width` - 列宽度（像素）
- `fixed: 'left'` - 固定列到左侧
- `align: 'center'` - 列内容居中对齐

### 2.7 基础方法定义

```typescript
// 方法定义
const goBack = () => {
  router.back();
};

const refreshData = async () => {
  loading.value = true;
  try {
    await loadDeviceList();
    message.success('数据刷新成功');
  } catch (error) {
    message.error('数据刷新失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = async () => {
  pagination.current = 1;
  await loadDeviceList();
};

const handleReset = () => {
  filters.deviceCode = '';
  filters.region = '';
  filters.deviceStatus = '';
  filters.dateRange = [];
  handleSearch();
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadDeviceList();
};
```

**代码解释：**
- `router.back()` - 路由返回上一页
- `async/await` - 异步函数语法，处理异步操作
- `try/catch/finally` - 错误处理结构
- `loading.value = true` - 设置加载状态
- `message.success()` - 显示成功提示
- `pagination.current = 1` - 重置分页到第一页

### 2.8 设备详情和预警处理

```typescript
const showDeviceDetail = async (record: DeviceContainer) => {
  try {
    loading.value = true;
    // 🎭 演示数据 - 模拟API调用获取分光器详情
    // 🔌 后端对接：需要替换为真实API调用
    // 实际应该调用：await api.getSplitterDetails(record.设备编码)
    splitterDetails.value = await mockGetSplitterDetails(record.设备编码);
    detailModalVisible.value = true;
  } catch (error) {
    message.error('获取设备详情失败');
  } finally {
    loading.value = false;
  }
};

const handleWarning = (record: DeviceContainer) => {
  // 🔌 后端对接 - 处理预警逻辑，暂时显示提示
  // 实际应该调用：await api.handleDeviceWarning(record.设备编码)
  message.info(`处理设备 ${record.设备编码} 的预警`);
};

const showPredictionModal = () => {
  // 重置预测配置和数据
  predictionConfig.selectedDevice = '';
  predictionConfig.period = 'week';
  predictionData.value = { hasData: false } as PredictionAnalysis;

  predictionModalVisible.value = true;
};
```

**代码解释：**
- `record: DeviceContainer` - 参数类型注解，确保传入正确的数据类型
- `await mockGetSplitterDetails()` - 调用模拟 API 获取数据
- `splitterDetails.value = ...` - 更新响应式数据
- `detailModalVisible.value = true` - 显示模态框
- 模板字符串 `${record.设备编码}` - 动态插入变量值

### 2.9 预测分析相关方法

```typescript
// 预测配置变化处理
const onPredictionPeriodChange = () => {
  if (predictionData.value.hasData) {
    executePrediction();
  }
};

// 执行预测分析
const executePrediction = async () => {
  if (!predictionConfig.selectedDevice || !predictionConfig.selectedDevice.trim()) {
    message.warning('请输入设备编码');
    return;
  }

  // 验证设备编码是否存在
  const deviceExists = deviceList.value.some(device =>
    device.设备编码 === predictionConfig.selectedDevice.trim()
  );

  if (!deviceExists) {
    message.warning('设备编码不存在，请检查输入');
    return;
  }

  try {
    predictionLoading.value = true;

    // 🎭 演示数据 - 模拟单设备预测API调用
    // 🔌 后端对接：需要替换为真实API调用
    // 实际应该调用：await api.predictSingleDevice(deviceCode, period)
    predictionData.value = await mockGetSingleDevicePrediction(
      predictionConfig.selectedDevice.trim(),
      predictionConfig.period
    );

    // 等待DOM更新后初始化图表
    await nextTick();
    // 添加延迟确保模态框完全渲染
    setTimeout(() => {
      initChart();
    }, 100);
  } catch (error) {
    console.error('预测分析失败:', error);
    message.error('预测分析失败');
  } finally {
    predictionLoading.value = false;
  }
};

const handleExport = () => {
  // 🔌 后端对接 - 导出功能开发中
  // 实际应该调用：await api.exportDeviceData(filters)
  message.info('导出功能开发中...');
};
```

**代码解释：**
- `trim()` - 去除字符串首尾空格
- `some()` - 数组方法，检查是否有元素满足条件
- `nextTick()` - Vue 的 DOM 更新完成回调
- `setTimeout()` - 延迟执行，确保 DOM 完全渲染
- `console.error()` - 输出错误信息到控制台

### 2.10 批量预测和设备预测详情

```typescript
// 全部预测方法
const executeAllPrediction = async () => {
  try {
    allPredictionLoading.value = true;
    message.info('开始执行全部设备预测...');

    // 🎭 演示数据 - 模拟批量预测API调用
    // 🔌 后端对接：需要替换为真实API调用
    // 实际应该调用：await api.predictAllDevices()
    const predictions = await mockGetAllDevicesPrediction();

    // 更新设备列表的预测数据
    deviceList.value = deviceList.value.map(device => {
      const prediction = predictions.find(p => p.设备编码 === device.设备编码);
      if (prediction) {
        return {
          ...device,
          预测实占率: prediction.预测实占率,
          预测空闲数: prediction.预测空闲数,
          预测状态: prediction.预测状态
        };
      }
      return device;
    });

    message.success(`预测完成！共预测 ${predictions.length} 个设备`);
  } catch (error) {
    message.error('全部预测执行失败');
  } finally {
    allPredictionLoading.value = false;
  }
};

// 显示设备预测详情
const showDevicePrediction = async (record: DeviceContainer) => {
  try {
    selectedDeviceForPrediction.value = record;
    devicePredictionModalVisible.value = true;
    devicePredictionData.value = { hasData: false } as DevicePredictionAnalysis;

    // 🎭 演示数据 - 加载设备预测详情数据
    // 🔌 后端对接：需要替换为真实API调用
    // 实际应该调用：await api.getDevicePredictionDetail(record.设备编码)
    devicePredictionData.value = await mockGetDevicePredictionDetail(record.设备编码);

    // 等待DOM更新后初始化图表
    await nextTick();
    // 添加额外的延迟确保模态框完全渲染
    setTimeout(() => {
      initDevicePredictionChart();
    }, 200);
  } catch (error) {
    message.error('获取设备预测详情失败');
  }
};

// 处理设备预测模态框关闭
const handleDevicePredictionModalClose = () => {
  // 重置数据
  devicePredictionData.value = { hasData: false } as DevicePredictionAnalysis;
  selectedDeviceForPrediction.value = null;
};
```

**代码解释：**
- `map()` - 数组方法，返回新数组，每个元素都经过处理
- `find()` - 数组方法，查找第一个满足条件的元素
- `...device` - 展开运算符，复制对象的所有属性
- 对象合并 - 使用展开运算符合并对象属性
- 数据重置 - 关闭模态框时清理数据，避免数据污染

### 2.11 趋势图相关方法

```typescript
// 趋势图相关方法
const toggleTrendChart = () => {
  trendChartCollapsed.value = !trendChartCollapsed.value;
};

const onSearchTrend = (searchCode: string) => {
  if (!searchCode.trim()) {
    message.warning('请输入设备编码或分光器编码');
    return;
  }

  trendConfig.currentDevice = searchCode.trim();
  loadTrendData();
};

const onTimeRangeChange = () => {
  if (trendConfig.currentDevice) {
    loadTrendData();
  }
};

const clearTrendSelection = () => {
  trendConfig.searchCode = '';
  trendConfig.currentDevice = '';
  trendData.value = [];
};
```

**代码解释：**
- `!trendChartCollapsed.value` - 逻辑非操作，切换布尔值
- 参数验证 - 检查输入是否为空
- 条件执行 - 只有在有当前设备时才加载数据
- 数据清空 - 重置所有相关状态

### 2.12 工具方法集合

```typescript
// 工具方法
const getProgressColor = (rate: number) => {
  if (rate >= 90) return '#ff4d4f';
  if (rate >= 80) return '#faad14';
  return '#52c41a';
};

const getStatusColor = (status: string) => {
  const colors = {
    normal: 'green',
    attention: 'orange',
    alarm: 'red'
  };
  return colors[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    attention: '注意',
    alarm: '告警'
  };
  return texts[status] || '未知';
};

const getTrendColor = (trend: string) => {
  const colors = {
    increasing: 'red',
    stable: 'blue',
    decreasing: 'green'
  };
  return colors[trend] || 'default';
};

const getTrendText = (trend: string) => {
  const texts = {
    increasing: '上升',
    stable: '稳定',
    decreasing: '下降'
  };
  return texts[trend] || '未知';
};

const getPriorityColor = (priority: string) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  };
  return colors[priority] || 'default';
};

const getPriorityText = (priority: string) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  };
  return texts[priority] || '未知';
};

// 计算实占率
const calculateOccupancyRate = (record: any) => {
  if (!record.分光器容量 || record.分光器容量 === 0) return 0;
  const rate = 1 - (record.分光器空闲数 / record.分光器容量);
  return Math.max(0, Math.min(100, Math.round(rate * 100)));
};

// 计算健康状态
const calculateHealthStatus = (occupancyRate: number) => {
  if (occupancyRate >= 90) return 'alarm';     // 告警
  if (occupancyRate >= 80) return 'attention'; // 注意
  return 'normal';                             // 正常
};
```

**代码解释：**
- 条件判断 - 根据数值范围返回不同颜色
- 对象映射 - 使用对象作为映射表，提高代码可读性
- `|| 'default'` - 逻辑或操作，提供默认值
- 数学计算 - 使用数学公式计算实占率
- `Math.max()` 和 `Math.min()` - 限制数值范围
- `Math.round()` - 四舍五入取整

### 2.13 预测状态相关方法

```typescript
// 预测状态相关方法
const getPredictionStatusColor = (status: string) => {
  const colors = {
    normal: 'green',
    attention: 'orange',
    alarm: 'red',
    expansion: 'purple'
  };
  return colors[status] || 'default';
};

const getPredictionStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    attention: '注意',
    alarm: '告警',
    expansion: '扩容建议'
  };
  return texts[status] || '未知';
};

const getRiskLevelColor = (level: string) => {
  const colors = {
    low: 'green',
    medium: 'orange',
    high: 'red'
  };
  return colors[level] || 'default';
};

const getRiskLevelText = (level: string) => {
  const texts = {
    low: '低',
    medium: '中',
    high: '高'
  };
  return texts[level] || '未知';
};
```

**代码解释：**
- 扩展状态映射 - 包含更多的状态类型
- 一致的命名模式 - 所有类似功能的方法使用相同的命名规则
- 颜色和文本分离 - 分别处理显示颜色和显示文本

### 2.14 数据加载方法

```typescript
// 数据加载方法
const loadDeviceList = async () => {
  loading.value = true;
  try {
    // 🎭 演示数据 - 模拟API调用
    // 🔌 后端对接：需要替换为真实API调用
    // 实际应该调用：await api.getDeviceList(pagination, filters)
    const response = await mockGetDeviceList();
    deviceList.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    message.error('加载设备列表失败');
  } finally {
    loading.value = false;
  }
};
```

**代码解释：**
- 异步数据加载 - 使用 async/await 处理异步操作
- 响应式数据更新 - 直接修改 .value 属性更新数据
- 分页信息更新 - 同时更新数据和分页总数
- 统一错误处理 - 使用 try/catch 处理可能的错误

### 2.15 图表初始化

```typescript
// 图表初始化
const { setOptions } = useECharts(chartRef);
const { setOptions: setTrendOptions } = useECharts(trendChartRef);
const { setOptions: setDevicePredictionOptions } = useECharts(devicePredictionChartRef);

// 趋势图数据
const trendData = ref<TrendData[]>([]);

interface TrendData {
  deviceCode: string;
  data: Array<{
    week: string;
    occupancyRate: number;
  }>;
}
```

**代码解释：**
- `useECharts()` - 自定义 Hook，返回图表操作方法
- 解构赋值 - 从返回对象中提取 setOptions 方法
- 重命名 - 使用 `setOptions: setTrendOptions` 避免命名冲突
- 接口定义 - 定义趋势图数据的结构

### 2.16 趋势数据加载和图表初始化

```typescript
// 加载趋势数据
const loadTrendData = async () => {
  if (!trendConfig.currentDevice) return;

  try {
    loading.value = true;
    // 🎭 演示数据 - 模拟趋势数据API调用
    // 🔌 后端对接：需要替换为真实API调用
    // 实际应该调用：await api.getTrendData(deviceCode, timeRange)
    trendData.value = await mockGetTrendData(
      [trendConfig.currentDevice],
      trendConfig.timeRange
    );

    nextTick(() => {
      initTrendChart();
    });
  } catch (error) {
    message.error('加载趋势数据失败');
  } finally {
    loading.value = false;
  }
};

// 初始化趋势图
const initTrendChart = () => {
  if (!trendData.value.length) return;

  const deviceData = trendData.value[0];
  const weeks = deviceData.data.map(item => item.week);
  const occupancyRates = deviceData.data.map(item => item.occupancyRate);

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.axisValue}<br/>实占率: ${param.value}%`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: weeks,
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      name: '实占率',
      type: 'line',
      data: occupancyRates,
      smooth: true,
      lineStyle: {
        color: '#1890ff',
        width: 3
      },
      symbol: 'circle',
      symbolSize: 8,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: '#1890ff40'
          }, {
            offset: 1,
            color: '#1890ff10'
          }]
        }
      }
    }]
  };

  setTrendOptions(option);
};
```

**代码解释：**
- 前置检查 - 确保有当前设备才执行
- 数组操作 - 使用 map 方法提取数据
- ECharts 配置 - 详细的图表配置对象
- `tooltip` - 鼠标悬停提示配置
- `grid` - 图表网格配置
- `xAxis/yAxis` - 坐标轴配置
- `series` - 数据系列配置
- `areaStyle` - 区域填充样式，使用渐变色

### 2.17 设备预测图表初始化

```typescript
// 初始化设备预测图表
const initDevicePredictionChart = () => {
  console.log('initDevicePredictionChart 被调用');
  console.log('devicePredictionData.value:', devicePredictionData.value);

  if (!devicePredictionData.value.hasData || !devicePredictionData.value.historicalData) {
    console.warn('设备预测数据不存在', {
      hasData: devicePredictionData.value.hasData,
      historicalData: devicePredictionData.value.historicalData
    });
    return;
  }

  // 确保DOM元素存在
  if (!devicePredictionChartRef.value) {
    console.warn('设备预测图表DOM元素不存在');
    return;
  }

  console.log('开始初始化设备预测详情图表');
  try {
  const historicalData = devicePredictionData.value.historicalData;
  const weeks = historicalData.map(item => item.week);
  const occupancyRates = historicalData.map(item => item.occupancyRate);

  // 添加预测数据点
  const predictionWeeks = ['第5周', '第6周'];
  const predictionRates = [
    devicePredictionData.value.prediction?.nextWeekRate || 0,
    devicePredictionData.value.prediction?.nextMonthRate || 0
  ];

  const allWeeks = [...weeks, ...predictionWeeks];
  const allRates = [...occupancyRates, ...Array(predictionWeeks.length).fill(null)];
  const predictionData = [...Array(weeks.length).fill(null), ...predictionRates];

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          if (param.value !== null) {
            result += `${param.marker}${param.seriesName}: ${param.value}%<br/>`;
          }
        });
        return result;
      }
    },
    legend: {
      data: ['历史数据', '预测数据'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: allWeeks,
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '历史数据',
        type: 'line',
        data: allRates,
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 3
        },
        symbol: 'circle',
        symbolSize: 8,
        connectNulls: false
      },
      {
        name: '预测数据',
        type: 'line',
        data: predictionData,
        smooth: true,
        lineStyle: {
          color: '#ff4d4f',
          width: 3,
          type: 'dashed'
        },
        symbol: 'diamond',
        symbolSize: 10,
        connectNulls: false
      }
    ]
  };

    setDevicePredictionOptions(option);
    console.log('设备预测详情图表初始化成功');
  } catch (error) {
    console.error('初始化设备预测图表失败:', error);
    message.error('图表初始化失败');
  }
};
```

**代码解释：**
- 调试日志 - 使用 console.log 输出调试信息
- 数据验证 - 多层检查确保数据完整性
- DOM 检查 - 确保图表容器元素存在
- 数据处理 - 合并历史数据和预测数据
- 展开运算符 - 使用 `...` 合并数组
- `Array().fill(null)` - 创建空值数组作为占位
- 双系列图表 - 同时显示历史数据和预测数据
- 不同样式 - 历史数据用实线，预测数据用虚线
- 错误处理 - 完整的 try/catch 错误处理

### 2.18 单设备预测图表初始化

```typescript
const initChart = () => {
  console.log('initChart 被调用');
  console.log('predictionData.value:', predictionData.value);

  if (!predictionData.value.hasData || !predictionData.value.historicalData) {
    console.warn('单设备预测数据不存在', {
      hasData: predictionData.value.hasData,
      historicalData: predictionData.value.historicalData
    });
    return;
  }

  // 确保DOM元素存在
  if (!chartRef.value) {
    console.warn('单设备预测图表DOM元素不存在');
    return;
  }

  console.log('开始初始化单设备预测图表');
  try {
    const historicalData = predictionData.value.historicalData;
    const weeks = historicalData.map(item => item.week);
    const occupancyRates = historicalData.map(item => item.occupancyRate);

    // 添加预测数据点
    const predictionWeeks = ['第5周', '第6周'];
    const predictionRates = [
      predictionData.value.prediction?.nextWeekRate || 0,
      predictionData.value.prediction?.nextMonthRate || 0
    ];

    const allWeeks = [...weeks, ...predictionWeeks];
    const allRates = [...occupancyRates, ...Array(predictionWeeks.length).fill(null)];
    const predictionDataPoints = [...Array(weeks.length).fill(null), ...predictionRates];

    const option = {
      title: {
        text: '端口使用率趋势预测',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            if (param.value !== null) {
              result += `${param.marker}${param.seriesName}: ${param.value}%<br/>`;
            }
          });
          return result;
        }
      },
      legend: {
        data: ['历史数据', '预测数据'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: allWeeks,
        axisLabel: {
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '历史数据',
          type: 'line',
          data: allRates,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 3
          },
          symbol: 'circle',
          symbolSize: 8,
          connectNulls: false
        },
        {
          name: '预测数据',
          type: 'line',
          data: predictionDataPoints,
          smooth: true,
          lineStyle: {
            color: '#ff4d4f',
            width: 3,
            type: 'dashed'
          },
          symbol: 'diamond',
          symbolSize: 10,
          connectNulls: false
        }
      ]
    };

    setOptions(option);
    console.log('单设备预测图表初始化成功');
  } catch (error) {
    console.error('初始化单设备预测图表失败:', error);
    message.error('图表初始化失败');
  }
};
```

**代码解释：**
- 与设备预测图表类似的结构和逻辑
- 添加了图表标题 `title` 配置
- 使用不同的变量名避免冲突 `predictionDataPoints`
- 调用不同的 setOptions 方法 `setOptions(option)`

### 2.19 生命周期钩子

```typescript
// 生命周期
onMounted(() => {
  loadDeviceList();
});
</script>
```

**代码解释：**
- `onMounted()` - Vue 3 生命周期钩子，组件挂载后执行
- 自动加载数据 - 页面加载完成后立即获取设备列表

---

## 第三部分：Style 样式代码详解

### 3.1 页面头部样式

```less
<style scoped lang="less">
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .back-btn {
      padding: 4px 8px;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }
  }
}
```

**代码解释：**
- `scoped` - 样式作用域限制，只影响当前组件
- `lang="less"` - 使用 Less 预处理器
- `display: flex` - 弹性布局
- `justify-content: space-between` - 两端对齐
- `gap: 12px` - 子元素间距
- `&:hover` - Less 嵌套语法，表示悬停状态
- 颜色值使用十六进制表示

### 3.2 统计卡片样式

```less
.statistics-cards {
  margin-bottom: 16px;

  .stat-card {
    height: 100px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.normal {
      border-left: 4px solid #52c41a;
    }

    &.attention {
      border-left: 4px solid #faad14;
    }

    &.alarm {
      border-left: 4px solid #ff4d4f;
    }

    &.total {
      border-left: 4px solid #1890ff;
    }
  }

  .update-time {
    margin-top: 12px;
    text-align: center;
    color: #8c8c8c;
    font-size: 14px;

    .anticon {
      margin-right: 8px;
    }
  }
}
```

**代码解释：**
- `transition: all 0.3s` - 过渡动画，0.3秒
- `transform: translateY(-2px)` - 向上移动2像素
- `box-shadow` - 阴影效果，使用 rgba 透明色
- `&.normal` - 类名组合选择器
- `border-left` - 左边框作为状态指示器
- `.anticon` - Ant Design 图标的类名

### 3.3 表格和操作按钮样式

```less
.table-card {
  .occupancy-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .ant-progress {
      flex: 1;
      margin: 0;
    }

    .occupancy-text {
      font-weight: 500;
      min-width: 40px;
    }
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #e6f7ff !important;
  }

  .no-prediction {
    color: #8c8c8c;
    font-style: italic;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0;
    min-width: 160px;

    .action-btn {
      min-width: 48px;
      text-align: center;
      padding: 4px 8px;
      margin-right: 4px;

      &:last-child {
        margin-right: 0;
      }
    }

    .action-btn-placeholder {
      min-width: 48px;
      height: 22px;
      display: inline-block;
      margin-right: 4px;
    }
  }
}
```

**代码解释：**
- `flex: 1` - 弹性增长，占据剩余空间
- `:deep()` - Vue 3 深度选择器，穿透 scoped 限制
- `!important` - 提高样式优先级
- `font-style: italic` - 斜体字
- `min-width` - 最小宽度，确保布局一致性
- `&:last-child` - 最后一个子元素选择器

### 3.4 响应式设计

```less
// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .statistics-cards {
    :deep(.ant-col) {
      margin-bottom: 8px;
    }
  }

  .filter-card {
    :deep(.ant-form) {
      .ant-form-item {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}
</style>
```

**代码解释：**
- `@media (max-width: 768px)` - 媒体查询，移动端适配
- `flex-direction: column` - 垂直排列
- `align-items: flex-start` - 左对齐
- 移动端优化 - 调整布局和间距

---

## 第四部分：演示数据与后端对接详细说明

### 4.1 所有演示数据汇总

#### 4.1.1 统计数据 (statistics)
```typescript
// 🎭 演示数据 - 需要后端接口：GET /api/splitter/statistics
const statistics = reactive({
  normalDevices: 156,        // 正常设备数量
  attentionDevices: 23,      // 注意设备数量
  alarmDevices: 5,           // 告警设备数量
  totalDevices: 184,         // 总设备数量
  lastUpdateTime: '2024-01-15 14:30:25'  // 最后更新时间
});
```

#### 4.1.2 区域选项数据
```vue
<!-- 🎭 演示数据 - 需要后端接口：GET /api/regions -->
<a-select-option value="常州">常州</a-select-option>
<a-select-option value="无锡">无锡</a-select-option>
<a-select-option value="苏州">苏州</a-select-option>
```

#### 4.1.3 设备列表数据 (deviceList)
```typescript
// 🎭 演示数据 - 来自 mockGetDeviceList() 方法
// 需要后端接口：GET /api/splitter/devices
const rawData = [
  {
    设备编码: 'CZ-FG-001',           // 设备唯一标识
    区域: '常州',                    // 设备所在区域
    小区入库时间: '2023-03-15',      // 设备入库时间
    覆盖的工程级的线路到达房间数: 320, // 覆盖房间数
    分光器数: 8,                     // 分光器数量
    分光器容量: 256,                 // 总容量
    分光器空闲数: 45,                // 空闲端口数
    ftth终端数: 185                  // FTTH终端数
  }
  // ... 更多设备数据
];
```

#### 4.1.4 分光器详情数据 (splitterDetails)
```typescript
// 🎭 演示数据 - 来自 mockGetSplitterDetails() 方法
// 需要后端接口：GET /api/splitter/device/{deviceCode}/splitters
const rawDetails = [
  {
    分光器编码: 'CZ-FG-001-SP-001',   // 分光器唯一标识
    安装地址: '1号机房A架',           // 安装位置
    分光器容量: 32,                  // 分光器容量
    分光器空闲数: 8,                 // 空闲端口数
    入网时间: '2023-03-15',          // 入网时间
    数据更新时间: '2024-01-15 14:25:30'  // 数据更新时间
  }
  // ... 更多分光器数据
];
```

#### 4.1.5 预测分析数据 (predictionData)
```typescript
// 🎭 演示数据 - 来自 mockGetSingleDevicePrediction() 方法
// 需要后端接口：POST /api/splitter/predict/single
const mockPrediction = {
  hasData: true,
  设备编码: deviceCode,
  historicalData: [                    // 历史数据
    { week: '第1周', occupancyRate: 65 },
    { week: '第2周', occupancyRate: 70 },
    { week: '第3周', occupancyRate: 75 },
    { week: '第4周', occupancyRate: 82 }
  ],
  prediction: {                        // 预测结果
    nextWeekRate: 87,                  // 下周预测占用率
    nextMonthRate: 95,                 // 下月预测占用率
    trend: 'increasing'                // 趋势：上升/稳定/下降
  },
  recommendations: {                   // 建议方案
    expansionAdvice: '建议在2周内增加2个32口分光器',
    adjustmentAdvice: '可将部分用户迁移至邻近设备',
    priority: 'high'                   // 优先级：高/中/低
  }
};
```

### 4.2 后端接口需求详细说明

#### 4.2.1 Controller 层接口设计

```java
// 🔌 后端对接 - 建议的 Controller 接口设计
@RestController
@RequestMapping("/api/splitter")
public class SplitterController {

    // 获取统计数据
    @GetMapping("/statistics")
    public ResponseEntity<StatisticsDTO> getStatistics() {
        // Service 层处理业务逻辑
        // DAO 层查询数据库
    }

    // 获取设备列表（支持分页和筛选）
    @GetMapping("/devices")
    public ResponseEntity<PageResult<DeviceDTO>> getDevices(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String deviceCode,
        @RequestParam(required = false) String region,
        @RequestParam(required = false) String deviceStatus,
        @RequestParam(required = false) String startDate,
        @RequestParam(required = false) String endDate
    ) {
        // 处理分页和筛选逻辑
    }

    // 获取设备详情
    @GetMapping("/device/{deviceCode}/splitters")
    public ResponseEntity<List<SplitterDetailDTO>> getSplitterDetails(
        @PathVariable String deviceCode
    ) {
        // 根据设备编码查询分光器详情
    }

    // 单设备预测
    @PostMapping("/predict/single")
    public ResponseEntity<PredictionResultDTO> predictSingleDevice(
        @RequestBody PredictionRequestDTO request
    ) {
        // 调用预测算法服务
    }

    // 批量预测
    @PostMapping("/predict/batch")
    public ResponseEntity<List<BatchPredictionDTO>> predictAllDevices() {
        // 批量预测所有设备
    }

    // 获取趋势数据
    @GetMapping("/trend")
    public ResponseEntity<TrendDataDTO> getTrendData(
        @RequestParam String deviceCode,
        @RequestParam String timeRange
    ) {
        // 查询历史趋势数据
    }

    // 导出数据
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportDeviceData(
        @RequestParam(required = false) String deviceCode,
        @RequestParam(required = false) String region,
        @RequestParam(required = false) String deviceStatus
    ) {
        // 导出 Excel 或 CSV 文件
    }

    // 获取区域列表
    @GetMapping("/regions")
    public ResponseEntity<List<String>> getRegions() {
        // 查询所有可用区域
    }

    // 处理设备预警
    @PostMapping("/device/{deviceCode}/handle-warning")
    public ResponseEntity<Void> handleDeviceWarning(
        @PathVariable String deviceCode,
        @RequestBody WarningHandleDTO request
    ) {
        // 处理设备预警逻辑
    }
}
```

#### 4.2.2 Service 层业务逻辑

```java
// 🔌 后端对接 - 建议的 Service 层设计
@Service
public class SplitterService {

    @Autowired
    private SplitterDAO splitterDAO;

    @Autowired
    private PredictionAlgorithmService predictionService;

    // 计算设备统计数据
    public StatisticsDTO calculateStatistics() {
        // 📊 计算逻辑 - 统计各状态设备数量
        // 可以在数据库层面计算，也可以在应用层计算
    }

    // 计算实占率
    public double calculateOccupancyRate(int capacity, int freeCount) {
        // 📊 计算逻辑 - 实占率 = (容量 - 空闲数) / 容量 * 100
        if (capacity == 0) return 0;
        return Math.max(0, Math.min(100, (1.0 - (double)freeCount / capacity) * 100));
    }

    // 计算健康状态
    public String calculateHealthStatus(double occupancyRate) {
        // 📊 计算逻辑 - 根据实占率判断健康状态
        if (occupancyRate >= 90) return "alarm";
        if (occupancyRate >= 80) return "attention";
        return "normal";
    }

    // 预测分析
    public PredictionResultDTO predictDevice(String deviceCode, String period) {
        // 调用机器学习算法或统计算法
        // 可能需要集成 Python 算法服务或使用 Java ML 库
    }
}
```

#### 4.2.3 DAO 层数据访问

```java
// 🔌 后端对接 - 建议的 DAO 层设计
@Repository
public interface SplitterDAO {

    // 查询设备列表
    @Select("SELECT * FROM device_container WHERE ...")
    List<DeviceContainer> findDevices(DeviceQueryDTO query);

    // 统计设备数量
    @Select("SELECT COUNT(*) FROM device_container WHERE health_status = #{status}")
    int countDevicesByStatus(String status);

    // 查询分光器详情
    @Select("SELECT * FROM splitter_detail WHERE device_code = #{deviceCode}")
    List<SplitterDetail> findSplittersByDeviceCode(String deviceCode);

    // 查询历史趋势数据
    @Select("SELECT * FROM device_history WHERE device_code = #{deviceCode} AND ...")
    List<HistoryData> findHistoryData(String deviceCode, String timeRange);

    // 更新预测结果
    @Update("UPDATE device_container SET predicted_rate = #{rate} WHERE device_code = #{code}")
    void updatePredictionResult(String deviceCode, double predictedRate);
}
```

### 4.3 数据库表结构建议

```sql
-- 🔌 后端对接 - 建议的数据库表结构

-- 设备容器表
CREATE TABLE device_container (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_code VARCHAR(50) UNIQUE NOT NULL COMMENT '设备编码',
    region VARCHAR(50) NOT NULL COMMENT '区域',
    install_time DATE COMMENT '小区入库时间',
    covered_rooms INT COMMENT '覆盖房间数',
    splitter_count INT COMMENT '分光器数量',
    splitter_capacity INT COMMENT '分光器总容量',
    splitter_free_count INT COMMENT '分光器空闲数',
    ftth_terminal_count INT COMMENT 'FTTH终端数',
    occupancy_rate DECIMAL(5,2) COMMENT '实占率',
    health_status VARCHAR(20) COMMENT '健康状态',
    predicted_rate DECIMAL(5,2) COMMENT '预测实占率',
    predicted_free_count INT COMMENT '预测空闲数',
    predicted_status VARCHAR(20) COMMENT '预测状态',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 分光器详情表
CREATE TABLE splitter_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    splitter_code VARCHAR(50) UNIQUE NOT NULL COMMENT '分光器编码',
    device_code VARCHAR(50) NOT NULL COMMENT '设备编码',
    install_address VARCHAR(200) COMMENT '安装地址',
    capacity INT COMMENT '分光器容量',
    free_count INT COMMENT '空闲数',
    occupancy_rate DECIMAL(5,2) COMMENT '实占率',
    network_time DATE COMMENT '入网时间',
    health_status VARCHAR(20) COMMENT '健康状态',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 历史数据表（用于趋势分析）
CREATE TABLE device_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_code VARCHAR(50) NOT NULL COMMENT '设备编码',
    record_date DATE NOT NULL COMMENT '记录日期',
    occupancy_rate DECIMAL(5,2) COMMENT '实占率',
    free_count INT COMMENT '空闲数',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 区域表
CREATE TABLE regions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    region_code VARCHAR(20) UNIQUE NOT NULL COMMENT '区域编码',
    region_name VARCHAR(50) NOT NULL COMMENT '区域名称',
    parent_region_code VARCHAR(20) COMMENT '父区域编码',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 第五部分：模块间相互作用分析

### 4.1 数据流向图

```
用户操作 → 事件处理方法 → 数据更新 → 视图更新
    ↓           ↓           ↓         ↓
  点击按钮 → handleSearch → filters → 表格重新渲染
  输入搜索 → onSearchTrend → trendConfig → 图表更新
  选择设备 → showDeviceDetail → splitterDetails → 模态框显示
```

### 4.2 核心交互流程

#### 4.2.1 设备列表加载流程
1. **组件挂载** - `onMounted()` 触发
2. **调用加载方法** - `loadDeviceList()`
3. **设置加载状态** - `loading.value = true`
4. **模拟API调用** - `mockGetDeviceList()`
5. **数据处理** - 计算实占率和健康状态
6. **更新响应式数据** - `deviceList.value = response.data`
7. **视图自动更新** - Vue 响应式系统触发重新渲染
8. **重置加载状态** - `loading.value = false`

#### 4.2.2 预测分析流程
1. **用户输入** - 在预测配置中输入设备编码
2. **数据验证** - 检查设备编码是否存在
3. **执行预测** - `executePrediction()` 方法
4. **API调用** - `mockGetSingleDevicePrediction()`
5. **数据更新** - `predictionData.value = result`
6. **DOM更新** - `await nextTick()`
7. **图表初始化** - `initChart()`
8. **ECharts渲染** - `setOptions(option)`

#### 4.2.3 图表渲染流程
1. **获取DOM引用** - `chartRef.value`
2. **数据验证** - 检查数据完整性
3. **数据处理** - 提取和转换图表数据
4. **配置生成** - 创建 ECharts 配置对象
5. **图表渲染** - 调用 `setOptions()` 方法
6. **错误处理** - try/catch 捕获异常

### 4.3 状态管理模式

#### 4.3.1 响应式数据管理
- **ref()** - 用于基本类型和DOM引用
- **reactive()** - 用于复杂对象和配置
- **computed()** - 计算属性（本文件中未使用）
- **watch()** - 监听器（本文件中未使用）

#### 4.3.2 事件驱动模式
```
用户事件 → 方法调用 → 状态变更 → 视图更新
```

### 4.4 组件通信机制

#### 4.4.1 父子组件通信
- **Props** - 向子组件传递数据
- **Emit** - 子组件向父组件发送事件
- **Slot** - 内容分发机制

#### 4.4.2 模态框通信
- **显示控制** - 通过 `v-model:open` 双向绑定
- **数据传递** - 通过响应式变量共享数据
- **事件处理** - 通过 `@cancel` 等事件处理关闭

### 4.5 性能优化策略

#### 4.5.1 懒加载和按需渲染
- **条件渲染** - 使用 `v-if` 避免不必要的DOM创建
- **延迟加载** - 使用 `setTimeout` 确保DOM完全渲染
- **数据验证** - 在处理前验证数据完整性

#### 4.5.2 内存管理
- **数据重置** - 模态框关闭时清理数据
- **事件解绑** - 组件销毁时自动清理
- **图表销毁** - ECharts 实例的生命周期管理

---

## 总结

这个 Vue 3 组件展示了现代前端开发的最佳实践：

1. **组件化设计** - 清晰的结构分离
2. **类型安全** - TypeScript 提供完整的类型检查
3. **响应式编程** - Vue 3 Composition API 的强大功能
4. **用户体验** - 丰富的交互和视觉反馈
5. **可维护性** - 良好的代码组织和注释
6. **扩展性** - 模块化的设计便于功能扩展

通过详细分析每个部分的代码，可以深入理解 Vue 3 + TypeScript + Ant Design Vue 技术栈的实际应用。

---

## 第七部分：模拟数据方法详细说明

### 7.1 所有 Mock 方法汇总

以下是文件中所有的模拟数据方法，这些都需要在实际项目中替换为真实的 API 调用：

#### 7.1.1 mockGetDeviceList() - 设备列表数据
```typescript
// 🎭 演示数据方法 - 需要替换为真实API调用
const mockGetDeviceList = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  const rawData = [
    {
      设备编码: 'CZ-FG-001',
      区域: '常州',
      小区入库时间: '2023-03-15',
      覆盖的工程级的线路到达房间数: 320,
      分光器数: 8,
      分光器容量: 256,
      分光器空闲数: 45,
      ftth终端数: 185
    },
    // ... 更多演示数据
  ];

  // 🔌 实际应该替换为：
  // const response = await axios.get('/api/splitter/devices', {
  //   params: { page: pagination.current, size: pagination.pageSize, ...filters }
  // });
  // return response.data;
};
```

#### 7.1.2 mockGetSplitterDetails() - 分光器详情数据
```typescript
// 🎭 演示数据方法 - 需要替换为真实API调用
const mockGetSplitterDetails = async (deviceCode: string) => {
  await new Promise(resolve => setTimeout(resolve, 800));

  const mockDetails = [
    {
      分光器编码: `${deviceCode}-SP-001`,
      安装地址: '1号机房A架',
      分光器容量: 32,
      分光器空闲数: 8,
      实占率: 75,
      入网时间: '2023-03-15',
      数据更新时间: '2024-01-15 14:25:30',
      健康状态: 'attention'
    },
    // ... 更多演示数据
  ];

  // 🔌 实际应该替换为：
  // const response = await axios.get(`/api/splitter/device/${deviceCode}/splitters`);
  // return response.data;
};
```

#### 7.1.3 mockGetSingleDevicePrediction() - 单设备预测数据
```typescript
// 🎭 演示数据方法 - 需要替换为真实API调用
const mockGetSingleDevicePrediction = async (deviceCode: string, period: string) => {
  await new Promise(resolve => setTimeout(resolve, 2000));

  const mockPrediction = {
    hasData: true,
    设备编码: deviceCode,
    historicalData: [
      { week: '第1周', occupancyRate: 65 },
      { week: '第2周', occupancyRate: 70 },
      { week: '第3周', occupancyRate: 75 },
      { week: '第4周', occupancyRate: 82 }
    ],
    prediction: {
      nextWeekRate: period === 'week' ? 87 : 85,
      nextMonthRate: period === 'month' ? 95 : 92,
      trend: 'increasing' as const
    },
    recommendations: {
      expansionAdvice: '建议在2周内增加2个32口分光器',
      adjustmentAdvice: '可将部分用户迁移至邻近设备',
      priority: 'high' as const
    }
  };

  // 🔌 实际应该替换为：
  // const response = await axios.post('/api/splitter/predict/single', {
  //   deviceCode,
  //   period
  // });
  // return response.data;
};
```

#### 7.1.4 mockGetDevicePredictionDetail() - 设备预测详情数据
```typescript
// 🎭 演示数据方法 - 需要替换为真实API调用
const mockGetDevicePredictionDetail = async (deviceCode: string) => {
  await new Promise(resolve => setTimeout(resolve, 1500));

  const mockDetail = {
    hasData: true,
    设备编码: deviceCode,
    historicalData: [
      { week: '第1周', occupancyRate: 65 },
      { week: '第2周', occupancyRate: 70 },
      { week: '第3周', occupancyRate: 75 },
      { week: '第4周', occupancyRate: 82 }
    ],
    prediction: {
      nextWeekRate: 87,
      nextMonthRate: 95,
      trend: 'increasing' as const
    },
    riskAssessment: {
      score: 85,
      level: 'high' as const,
      reasons: ['实占率持续上升', '预测将超过90%阈值', '空闲端口不足']
    },
    businessInsights: {
      trendAnalysis: '该设备实占率呈持续上升趋势，需要关注',
      expansionAdvice: '建议在未来2周内进行扩容',
      confidenceLevel: 92
    },
    recommendations: {
      expansionAdvice: '建议增加2个32口分光器',
      adjustmentAdvice: '可考虑负载均衡调整',
      priority: 'high' as const
    }
  };

  // 🔌 实际应该替换为：
  // const response = await axios.get(`/api/splitter/device/${deviceCode}/prediction-detail`);
  // return response.data;
};
```

#### 7.1.5 mockGetAllDevicesPrediction() - 批量预测数据
```typescript
// 🎭 演示数据方法 - 需要替换为真实API调用
const mockGetAllDevicesPrediction = async () => {
  await new Promise(resolve => setTimeout(resolve, 3000));

  const mockPredictions = [
    {
      设备编码: 'CZ-FG-001',
      预测实占率: 87,
      预测空闲数: 33,
      预测状态: 'attention' as const
    },
    {
      设备编码: 'CZ-FG-002',
      预测实占率: 95,
      预测空闲数: 13,
      预测状态: 'alarm' as const
    },
    // ... 更多演示数据
  ];

  // 🔌 实际应该替换为：
  // const response = await axios.post('/api/splitter/predict/batch');
  // return response.data;
};
```

#### 7.1.6 mockGetTrendData() - 趋势数据
```typescript
// 🎭 演示数据方法 - 需要替换为真实API调用
const mockGetTrendData = async (deviceCodes: string[], timeRange: string) => {
  await new Promise(resolve => setTimeout(resolve, 1200));

  const mockTrend = deviceCodes.map(deviceCode => ({
    deviceCode,
    data: [
      { week: '第1周', occupancyRate: 65 },
      { week: '第2周', occupancyRate: 70 },
      { week: '第3周', occupancyRate: 75 },
      { week: '第4周', occupancyRate: 82 },
      { week: '第5周', occupancyRate: 78 },
      { week: '第6周', occupancyRate: 85 }
    ]
  }));

  // 🔌 实际应该替换为：
  // const response = await axios.get('/api/splitter/trend', {
  //   params: { deviceCodes: deviceCodes.join(','), timeRange }
  // });
  // return response.data;
};
```

### 7.2 API 调用替换指南

#### 7.2.1 创建 API 服务文件
```typescript
// 🔌 建议创建 src/api/splitter.ts 文件
import { http } from '@/utils/http';

export const splitterApi = {
  // 获取设备列表
  getDeviceList: (params: any) =>
    http.get('/api/splitter/devices', { params }),

  // 获取统计数据
  getStatistics: () =>
    http.get('/api/splitter/statistics'),

  // 获取分光器详情
  getSplitterDetails: (deviceCode: string) =>
    http.get(`/api/splitter/device/${deviceCode}/splitters`),

  // 单设备预测
  predictSingleDevice: (data: { deviceCode: string; period: string }) =>
    http.post('/api/splitter/predict/single', data),

  // 批量预测
  predictAllDevices: () =>
    http.post('/api/splitter/predict/batch'),

  // 获取趋势数据
  getTrendData: (params: { deviceCode: string; timeRange: string }) =>
    http.get('/api/splitter/trend', { params }),

  // 导出数据
  exportData: (params: any) =>
    http.get('/api/splitter/export', { params, responseType: 'blob' }),

  // 获取区域列表
  getRegions: () =>
    http.get('/api/regions'),

  // 处理设备预警
  handleDeviceWarning: (deviceCode: string, data: any) =>
    http.post(`/api/splitter/device/${deviceCode}/handle-warning`, data)
};
```

#### 7.2.2 替换模拟方法的步骤
1. **导入 API 服务**：在组件中导入 `splitterApi`
2. **替换方法调用**：将所有 `mockXxx()` 方法替换为对应的 API 调用
3. **处理响应数据**：根据后端返回的数据格式调整前端处理逻辑
4. **错误处理**：添加适当的错误处理和用户提示
5. **加载状态**：保持现有的 loading 状态管理

### 7.3 替换示例

#### 替换前（演示数据）：
```typescript
const loadDeviceList = async () => {
  loading.value = true;
  try {
    const response = await mockGetDeviceList();  // 🎭 演示数据
    deviceList.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    message.error('加载设备列表失败');
  } finally {
    loading.value = false;
  }
};
```

#### 替换后（真实API）：
```typescript
const loadDeviceList = async () => {
  loading.value = true;
  try {
    const response = await splitterApi.getDeviceList({  // 🔌 真实API
      page: pagination.current,
      size: pagination.pageSize,
      ...filters
    });
    deviceList.value = response.data.records;  // 根据后端返回格式调整
    pagination.total = response.data.total;
  } catch (error) {
    message.error('加载设备列表失败');
  } finally {
    loading.value = false;
  }
};
```

这样的替换需要在所有使用模拟数据的地方进行，确保前端能够正确对接后端提供的真实数据接口。

---

## 📊 快速查找表

### 演示数据快速定位表

| 数据类型 | 变量名 | 模拟方法 | 需要的后端接口 | 说明 |
|---------|--------|----------|---------------|------|
| 统计数据 | `statistics` | 内置演示数据 | `GET /api/splitter/statistics` | 页面顶部统计卡片 |
| 设备列表 | `deviceList` | `mockGetDeviceList()` | `GET /api/splitter/devices` | 主表格数据源 |
| 分光器详情 | `splitterDetails` | `mockGetSplitterDetails()` | `GET /api/splitter/device/{code}/splitters` | 设备详情弹窗 |
| 单设备预测 | `predictionData` | `mockGetSingleDevicePrediction()` | `POST /api/splitter/predict/single` | 预测分析弹窗 |
| 设备预测详情 | `devicePredictionData` | `mockGetDevicePredictionDetail()` | `GET /api/splitter/device/{code}/prediction-detail` | 操作栏预测弹窗 |
| 批量预测 | 临时变量 | `mockGetAllDevicesPrediction()` | `POST /api/splitter/predict/batch` | 全部预测功能 |
| 趋势数据 | `trendData` | `mockGetTrendData()` | `GET /api/splitter/trend` | 趋势图数据 |
| 区域选项 | 硬编码 | 无 | `GET /api/regions` | 筛选下拉选项 |

### 计算逻辑快速定位表

| 计算功能 | 方法名 | 计算位置 | 说明 |
|---------|--------|----------|------|
| 实占率计算 | `calculateOccupancyRate()` | 前端 | (容量-空闲数)/容量*100 |
| 健康状态判断 | `calculateHealthStatus()` | 前端 | 根据实占率判断状态 |
| 进度条颜色 | `getProgressColor()` | 前端 | 根据实占率返回颜色 |
| 状态标签颜色 | `getStatusColor()` | 前端 | 状态到颜色的映射 |
| 趋势颜色 | `getTrendColor()` | 前端 | 趋势到颜色的映射 |
| 优先级颜色 | `getPriorityColor()` | 前端 | 优先级到颜色的映射 |

### 后端接口优先级表

| 优先级 | 接口 | 功能 | 是否必需 |
|-------|------|------|---------|
| 🔴 高 | `GET /api/splitter/devices` | 设备列表 | 必需 |
| 🔴 高 | `GET /api/splitter/statistics` | 统计数据 | 必需 |
| 🟡 中 | `GET /api/splitter/device/{code}/splitters` | 设备详情 | 重要 |
| 🟡 中 | `POST /api/splitter/predict/single` | 单设备预测 | 重要 |
| 🟢 低 | `POST /api/splitter/predict/batch` | 批量预测 | 可选 |
| 🟢 低 | `GET /api/splitter/trend` | 趋势数据 | 可选 |
| 🟢 低 | `GET /api/splitter/export` | 数据导出 | 可选 |
| 🟢 低 | `GET /api/regions` | 区域列表 | 可选 |

### 开发建议

1. **第一阶段**：实现高优先级接口，确保基本功能可用
2. **第二阶段**：实现中优先级接口，完善核心功能
3. **第三阶段**：实现低优先级接口，增强用户体验
4. **数据一致性**：确保前后端数据结构完全一致
5. **错误处理**：为每个接口添加适当的错误处理
6. **性能优化**：考虑分页、缓存等性能优化策略

---

## 🎯 结语

本文档详细解析了 `splitter-port.vue` 文件的每一行代码，特别标注了所有演示数据和后端对接需求。通过这份文档，前端开发者可以：

1. **理解代码结构**：掌握 Vue 3 + TypeScript 的开发模式
2. **识别演示数据**：清楚哪些数据需要替换为真实API
3. **对接后端**：了解需要哪些后端接口支持
4. **维护代码**：具备修改和扩展功能的能力

对于后端开发者，可以根据文档中的接口设计建议和数据结构定义，快速搭建对应的后端服务。

希望这份文档能够帮助团队更好地理解和维护这个前端组件！
