{"cells": [{"cell_type": "markdown", "id": "445fb4b1-a281-47e1-971f-4c435427365f", "metadata": {}, "source": ["## 练习3 顺序链的搭建\n", "\n", "**业务场景** ： 公司要投放一款产品，请根据产品的简介生成一个响亮的品牌名称。然后，生成一个小红书的文案。\n", "\n", "为了让更多人阅读，要再生成3个不一样的评论。越真实越好。\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f6bfd2e8-2b83-4204-9e6e-ce3c3d05dbb3", "metadata": {}, "outputs": [], "source": ["#创建和大模型的连接\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "url_ds = \"https://api.deepseek.com\"\n", "deepseek_key=os.environ[\"DS_API_KEY\"]\n", "\n", "chat = ChatOpenAI(model=\"deepseek-chat\", # V3: deepseek-chat;  R1: deepseek-reason\n", "                    base_url=url_ds,\n", "                    api_key=deepseek_key)"]}, {"cell_type": "code", "execution_count": 2, "id": "7b94ffcb-ae46-4617-b66a-3807688d77ae", "metadata": {}, "outputs": [], "source": ["from langchain.chains import SequentialChain\n", "from langchain.chains import LLMChain\n", "from langchain.prompts import ChatPromptTemplate"]}, {"cell_type": "code", "execution_count": 8, "id": "49efcf0a-5eb0-4230-9699-370b911fb23b", "metadata": {}, "outputs": [], "source": ["first_prompt=ChatPromptTemplate.from_template('''请根据产品介绍，设计一个品牌名称。要求：响亮，时尚. \n", "只要一个品牌名字. 不要说明，不要解释。\\n 产品介绍 {product}''')\n", "chain_01=LLMChain(llm=chat,prompt=first_prompt,output_key='brand')\n", "\n", "second_prompt=ChatPromptTemplate.from_template('''请根据产品介绍和品牌名称，写一段小红书的文案。\n", "目标客户：18-25岁，男性. 不要说明，不要解释。字数：500字。\\n 产品介绍 {product} \\n 品牌 {brand}''')\n", "chain_02=LLMChain(llm=chat,prompt=second_prompt,output_key='redbook')\n", "\n", "third_prompt=ChatPromptTemplate.from_template('''请根据产品文案，以一个用户的视角，写一段评论（字数50字以内）。\n", "不要说明，不要解释。文案：{redbook}''')\n", "chain_03=LLMChain(llm=chat,prompt=third_prompt,output_key='comment1')\n", "\n", "four_prompt=ChatPromptTemplate.from_template('''请根据产品文案，以一个用户的视角，写一段评论（字数50字以内）。\n", "不要说明，不要解释。不要和前面的评论重复，要另辟蹊径。文案：{redbook} 前面的评论:{comment1}''')\n", "chain_04=LLMChain(llm=chat,prompt=four_prompt,output_key='comment2')\n", "\n", "five_prompt=ChatPromptTemplate.from_template('''请根据产品文案，以一个用户的视角，写一段评论（字数50字以内）。\n", "不要说明，不要解释。不要和前面的评论重复，要另辟蹊径。文案：{redbook} 前面的评论:{comment1} {comment2}''')\n", "chain_05=LLMChain(llm=chat,prompt=five_prompt,output_key='comment3')\n", "\n", "overall_chain=Sequential<PERSON>hain(chains=[chain_01,chain_02,chain_03,chain_04,chain_05],\n", "                              input_variables=['product'],\n", "                              output_variables=['brand','redbook','comment1','comment2','comment3'],\n", "                              verbose=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "3a0e3dd8-50b8-4a2e-9d9a-61d0650501bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new SequentialChain chain...\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "AI设计的品牌： 动能闪充\n", "小红书文案 🏃‍♂️💥【动能闪充】跑步也能发电？这黑科技太炸了！  \n", "\n", "兄弟们！发现个逆天神器！跑个步居然能把手机电充满？⚡️【动能闪充】直接颠覆认知！  \n", "\n", "每次跑步耳机没电、手机红血条就焦虑？现在边跑边充电！戴上它，每一步都在发电🔥 不用插线、不用充电宝，跑5公里=手机回血50%！实测狂甩普通充电宝十条街！  \n", "\n", "🌟 磁吸黑科技+军工级防水，暴汗如雨照样稳如老狗！  \n", "🌟 20000mAh超大容量，跑一次够充3次AirPods！  \n", "🌟 夜跑党狂喜！自带RGB跑马灯，回头率直接拉爆！  \n", "\n", "上周马拉松戴着它，跑完全程手机电量从20%飙到85%！隔壁跑友都看傻了🤯 现在健身房撸铁也挂着，练完直接满电发朋友圈，连充电时间都省了！  \n", "\n", "👟 轻到没存在感！比AirPods充电盒还小，塞裤兜就能出门。学生党早八赶课、夜跑约会神器，再也不用抢图书馆插座了！  \n", "\n", "⚠️警告：这玩意儿容易上瘾！现在一天不跑步就浑身难受，毕竟每一步都是白嫖的电量啊！  \n", "\n", "🔥限时解锁「动能公式」：  \n", "跑步里程=充电速度=电量自由  \n", "#男生必备神器 #黑科技充电 #跑步发电\n", "------------------------\n", "用户13****： \"这玩意儿简直颠覆认知！跑完5公里手机回血50%，健身房撸铁还能充电，再也不用抢插座了！夜跑回头率爆表，黑科技真香！\"\n", "用户18****： \"马拉松实测电量从20%飙到85%！军工防水+磁吸稳得一批，RGB灯带夜跑超拉风，现在跑步=充电自由，彻底告别充电焦虑！\"\n", "用户16****： \"学生党早八救星！塞裤兜就能出门，跑着上课还能给手机回血，图书馆插座争夺战终结者！这波黑科技我跪了！\"\n"]}], "source": ["#启动chain\n", "product='这个产品是新型的充电设备，能够在你跑步的时候，带上它，自动产生电能。'\n", "res=overall_chain.invoke(product)\n", "print('AI设计的品牌：',res['brand'])\n", "print('小红书文案',res['redbook'])\n", "print('------------------------')\n", "print('用户13****：',res['comment1'])\n", "print('用户18****：',res['comment2'])\n", "print('用户16****：',res['comment3'])"]}, {"cell_type": "code", "execution_count": null, "id": "78bace13-a098-440d-836f-647ca4d50922", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}