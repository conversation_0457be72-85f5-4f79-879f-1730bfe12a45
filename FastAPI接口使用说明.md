# 分光器预测 FastAPI 接口使用说明

## 概述

`splitter_prediction_fastapi.py` 是基于原始预测代码 `hybridmodel_predictor_modular.py` 封装的现代化 FastAPI 接口，提供高性能的预测分析服务。

## FastAPI 相比 Flask 的优势

### 🚀 **性能优势**
- **异步支持**：原生异步处理，性能提升 3-10 倍
- **高并发**：支持更多并发请求
- **更快响应**：减少请求处理时间

### 📚 **开发体验**
- **自动文档生成**：访问 `http://localhost:8000/docs` 查看 Swagger UI
- **强类型检查**：基于 Pydantic 的数据验证
- **IDE 支持**：更好的代码提示和错误检查
- **现代化设计**：符合现代 API 开发标准

### 🔧 **集成优势**
- **OpenAPI 标准**：自动生成标准化 API 文档
- **数据验证**：自动验证请求数据格式
- **错误处理**：统一的错误响应格式
- **类型安全**：减少运行时错误

## 安装和启动

### 1. 安装依赖
```bash
pip install fastapi uvicorn pandas numpy scikit-learn tensorflow statsmodels
```

### 2. 启动服务
```bash
# 开发模式（自动重载）
uvicorn splitter_prediction_fastapi:app --host 0.0.0.0 --port 8000 --reload

# 生产模式
uvicorn splitter_prediction_fastapi:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 访问文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## API 接口详情

### 1. 健康检查接口

**接口地址：** `GET /api/health`

**功能：** 检查API服务是否正常运行

**响应示例：**
```json
{
    "status": "healthy",
    "service": "splitter-prediction-api",
    "version": "2.0.0",
    "tensorflow_available": true,
    "timestamp": "2024-12-25T10:30:00"
}
```

### 2. 单设备预测接口

**接口地址：** `POST /api/predict/single`

**功能：** 对单个设备进行预测分析

**请求模型：** `SinglePredictionRequest`
```json
{
    "deviceCode": "CZ-FG-001",
    "period": "week",
    "historicalData": [
        {
            "实占率": 0.85,
            "空闲端口数": 8,
            "分光器容量": 48,
            "潜在需求比": 0.6,
            "ftth终端数": 120,
            "覆盖的工程级的线路到达房间数": 200,
            "容量变化": 0
        }
    ]
}
```

**响应模型：** `SinglePredictionResponse`
```json
{
    "hasData": true,
    "设备编码": "CZ-FG-001",
    "prediction": {
        "nextWeekRate": 87.5,
        "nextMonthRate": 89.2,
        "trend": "increasing",
        "confidence": 85.0
    },
    "recommendations": {
        "expansionAdvice": "建议在2周内增加分光器容量",
        "adjustmentAdvice": "可考虑将部分用户迁移至邻近设备",
        "priority": "medium"
    },
    "historicalData": [...],
    "deviceType": "normal",
    "trendAnalysis": "📈 业务增长趋势 | 🎯 实占率85.0% | ✅ 运行正常"
}
```

### 3. 批量预测接口

**接口地址：** `POST /api/predict/batch`

**功能：** 批量预测多个设备

**请求模型：** `BatchPredictionRequest`
```json
{
    "devices": [
        {
            "deviceCode": "CZ-FG-001",
            "historicalData": [...]
        },
        {
            "deviceCode": "CZ-FG-002",
            "historicalData": [...]
        }
    ]
}
```

**响应模型：** `List[BatchPredictionItem]`
```json
[
    {
        "设备编码": "CZ-FG-001",
        "预测实占率": 87.5,
        "预测空闲数": 6,
        "预测状态": "attention",
        "deviceType": "stable"
    }
]
```

### 4. 趋势分析接口

**接口地址：** `POST /api/analyze/trend`

**功能：** 分析设备使用趋势

**请求模型：** `TrendAnalysisRequest`
```json
{
    "deviceCode": "CZ-FG-001",
    "timeRange": "recent",
    "historicalData": [...]
}
```

**响应模型：** `TrendAnalysisResponse`
```json
{
    "deviceCode": "CZ-FG-001",
    "trendAnalysis": "📈 业务增长趋势 | 🎯 实占率85.0% | ✅ 运行正常",
    "deviceType": "normal",
    "trendData": [
        {"week": "第1周", "occupancyRate": 85.2},
        {"week": "第2周", "occupancyRate": 86.1}
    ]
}
```

### 5. 风险评估接口

**接口地址：** `POST /api/analyze/risk`

**功能：** 评估设备风险等级

**请求模型：** `RiskAnalysisRequest`
```json
{
    "deviceCode": "CZ-FG-001",
    "currentData": {
        "实占率": 0.85,
        "空闲端口数": 8,
        "分光器容量": 48,
        "潜在需求比": 0.6
    }
}
```

**响应模型：** `RiskAnalysisResponse`
```json
{
    "deviceCode": "CZ-FG-001",
    "riskScore": 75.5,
    "riskLevel": "medium",
    "reasons": ["实占率较高（≥ 80%）", "空闲端口数较少（4-5个）"],
    "recommendations": "建议制定扩容计划，定期检查设备负载"
}
```

### 6. 配置获取接口

**接口地址：** `GET /api/config`

**功能：** 获取预测算法配置参数

**响应模型：** `ConfigResponse`
```json
{
    "healthThresholds": {
        "attention": 80,
        "alarm": 90
    },
    "predictionConfig": {
        "algorithm": "hybrid",
        "confidence": 85
    },
    "supportedDeviceTypes": ["stable", "expanded", "normal", "volatile", "insufficient_data"],
    "apiVersion": "2.0.0",
    "tensorflowAvailable": true
}
```

## 数据验证特性

### 自动数据验证
FastAPI 会自动验证所有请求数据：

```python
class HistoricalDataPoint(BaseModel):
    实占率: float = Field(..., ge=0, le=1, description="实占率，范围0-1")
    空闲端口数: int = Field(..., ge=0, description="空闲端口数")
    分光器容量: int = Field(..., gt=0, description="分光器容量")
    # ... 其他字段
```

### 错误响应格式
当数据验证失败时，会返回详细的错误信息：

```json
{
    "detail": [
        {
            "loc": ["body", "historicalData", 0, "实占率"],
            "msg": "ensure this value is less than or equal to 1",
            "type": "value_error.number.not_le",
            "ctx": {"limit_value": 1}
        }
    ]
}
```

## 与后端 Java 代码的对接

### HTTP 客户端调用示例

```java
@Service
public class FastApiPredictionClient {
    
    private final RestTemplate restTemplate;
    
    public FastApiPredictionClient() {
        this.restTemplate = new RestTemplate();
    }
    
    public JSONObject callSinglePrediction(String deviceCode, List<JSONObject> historicalData) {
        String url = "http://localhost:8000/api/predict/single";
        
        JSONObject request = new JSONObject();
        request.put("deviceCode", deviceCode);
        request.put("period", "week");
        request.put("historicalData", historicalData);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(request.toJSONString(), headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        
        return JSON.parseObject(response.getBody());
    }
}
```

### 在 SplitterServiceImpl 中集成

```java
@Autowired
private FastApiPredictionClient predictionClient;

@Override
public JSONObject executePredictionAlgorithm(List<JSONObject> historicalData, String period) {
    try {
        // 调用 FastAPI 预测接口
        JSONObject response = predictionClient.callSinglePrediction(deviceCode, historicalData);
        
        // 提取预测结果
        return response.getJSONObject("prediction");
        
    } catch (Exception e) {
        log.error("调用 FastAPI 预测失败", e);
        throw new RuntimeException("预测算法执行失败: " + e.getMessage());
    }
}
```

## 部署建议

### 开发环境
```bash
uvicorn splitter_prediction_fastapi:app --reload --port 8000
```

### 生产环境
```bash
# 使用 Gunicorn + Uvicorn Workers
gunicorn splitter_prediction_fastapi:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 或者直接使用 Uvicorn
uvicorn splitter_prediction_fastapi:app --host 0.0.0.0 --port 8000 --workers 4
```

### Docker 部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY splitter_prediction_fastapi.py .

EXPOSE 8000

CMD ["uvicorn", "splitter_prediction_fastapi:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 性能优化建议

1. **异步处理**：利用 FastAPI 的异步特性处理 I/O 密集型操作
2. **连接池**：配置数据库连接池
3. **缓存**：对频繁查询的数据进行缓存
4. **负载均衡**：使用多个 Worker 进程
5. **监控**：集成 Prometheus 等监控工具

## 总结

FastAPI 版本相比 Flask 版本具有以下优势：

- ✅ **更好的性能**：异步处理，支持高并发
- ✅ **自动文档**：无需手动编写 API 文档
- ✅ **类型安全**：强类型检查，减少错误
- ✅ **现代化**：符合现代 API 开发标准
- ✅ **易于维护**：清晰的代码结构和错误处理

推荐在新项目中使用 FastAPI 版本，以获得更好的开发体验和性能表现。
