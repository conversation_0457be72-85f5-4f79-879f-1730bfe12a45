{"cells": [{"cell_type": "markdown", "id": "73aaed0b-0cb6-45c4-9e48-0964e6020470", "metadata": {"ExecuteTime": {"end_time": "2025-04-23T23:04:17.348595Z", "start_time": "2025-04-23T23:03:33.315910Z"}}, "source": ["## 合同审核\n", "合同审核是每个企业都要做的事情，虽然简单，但是很枯燥。\n", "\n", "### 任务描述：\n", "* 识别扫描合同\n", "* 对照电子合同\n", "* 找到不一致的地方\n", "\n", "本次案例来自以下网址：\n", "\n", "http://ccgp-henan.gov.cn/henan/content?infoId=1450751621187974&channelCode=H601401\n"]}, {"cell_type": "code", "execution_count": 1, "id": "43f76154-6344-4cc5-85c1-5964495d898f", "metadata": {}, "outputs": [], "source": ["#创建Langchain的模板\n", "#与deepseek连接\n", "from langchain_openai import ChatOpenAI\n", "deepseek_key='***********************************'\n", "\n", "chat = ChatOpenAI(model=\"deepseek-chat\", \n", "                    base_url='https://api.deepseek.com',\n", "                    api_key=deepseek_key)\n", "#创建模板\n", "from langchain.prompts import ChatPromptTemplate\n", "template_str='''你是一个合同审核助手，你要审核合同电子数据和合同文本的数据是否一致。\n", "主要审核的内容包括：\n", "1. 合同编号是否一致\n", "2. 合同签署日期是否一致\n", "3. 合同金额是否一致\n", "4. 合同乙方是否一致  \n", "电子数据:{contract_base}.\n", "\n", "文本数据：{contract_paper}\n", "'''\n", "prompt_template=ChatPromptTemplate.from_template(template_str)"]}, {"cell_type": "code", "execution_count": 2, "id": "4e0dad85-6fb5-44a2-981a-c3e5d4719e3b", "metadata": {}, "outputs": [], "source": ["#创建一个chain\n", "chain=prompt_template | chat "]}, {"cell_type": "code", "execution_count": 3, "id": "e0a6de9b-567e-482b-9a0c-23efa3fd90f5", "metadata": {}, "outputs": [], "source": ["# 扫描合同的分析\n", "# pip install rapidocr-onnxruntime\n", "# pip install pdfminer.six\n", "#使用RapidOCRBlobParser实现图像识别技术\n", "from langchain_community.document_loaders.parsers import RapidOCRBlobParser\n", "from langchain_community.document_loaders import PDFMinerLoader\n", "loader = PDFMinerLoader(\n", "    \"产品销售合同.pdf\",\n", "    mode=\"page\",\n", "    images_inner_format=\"markdown-img\",\n", "    images_parser=RapidOCRBlobParser(),\n", ")\n", "scan_docs = loader.load()"]}, {"cell_type": "code", "execution_count": 4, "id": "c66a34a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["![产品销售合同\n", "买受人：商丘市环境卫生管理处\n", "合同编号：\n", "出卖人：烟台海德专用汽车有限公司\n", "签订地点：商丘\n", "依据《中华人民共和国合同法》及有关法规、条例的规定，双方本着友好合作，\n", "互利互惠的原则，经充分协商，通过正式授权代表签署以下合同条款，以便共同遵守。\n", "产品名称、数量、金额、交货期限：\n", "单价\n", "标的名称\n", "商标\n", "成格型号\n", "生产广家\n", "单位\n", "数量\n", "（万元\n", "万\n", "文身时\n", "元）\n", "扫路机\n", "海\n", "CHD5021TSL\n", "期台海德\n", "台\n", "7\n", "22.98\n", "160.86\n", "合同签订局\n", "7日内\n", "合计人现币全额（人写）查值随拾万任随低元整（Y1608600.00\n", "、产品质量、技术标准及期限：执行国家行业标准或出实人企业技术标准，保修题\n", "壹年（易损件除外）。\n", "三、随机的必备品、配件、工具数最及供应方法：按随车清单验收\n", "四、标的物所有权自付全款时起转移，但买受人未履行支付货款义务，标的物属于出\n", "卖人所有。\n", "五、知识产权条款\n", "1、出卖人销售产权若涉及知识产权，其权属归出卖人所有，买受人只能用于配\n", "套使用，不得进行生产、销售行为。\n", "2、若发现买受人有贴牌，盗牌行为，买受人应陪偿出卖人因该行为产生的所有\n", "损失，并停止贴牌、盗牌等侵权行为，因此行为产生的知识产权纠纷由买受人承担。\n", "3、解决知识产权纠纷按照（专利法）《商标法》等有关法规处理\n", "六、合同履行地点：出卖人所在地。\n", "七、运输方式及到站和费用负担：买受人委托出卖人代办托运，运费由出卖人承担。\n", "八、验收标准方法及期限：附件、配件齐全，买受人按照产品质量要求、技术标准现\n", "场验收。如果买受人对收到的货物存有异议，应在收到货物后5日内提出，逾则\n", "现为验收合格。\n", "九、成套设备的安装和调试：出卖人负责免费进行安装和调试指导，并对买受人操作](#)\n", "-------------\n"]}], "source": ["for x in scan_docs:\n", "    print(x.page_content)\n", "    print('-------------')"]}, {"cell_type": "code", "execution_count": 5, "id": "c514c8ea-87f6-4722-931e-c0bfd0eb0f0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata: {'source': 'D:\\\\我的培训\\\\250612 深圳通信\\\\图PDF的识别\\\\contract.json', 'seq_num': 1}\n", "Content: {\"合同名称\":\"产品销售合同\",\"项目编号\":\"商财采【2015】402号\",\"项目名称\":\"\",\"采购人(甲方)\":\"商丘市环境卫生管理处\",\"供应商(乙方)\":\"烟台海德专用汽车有限公司\",\"合同金额\":\"1608600万元\",\"合同签订日期\":\"2015-12-15\",\"合同公告日期\":\"2015-12-22\"}\n"]}], "source": ["#读取电子合同数据\n", "#pip install jq\n", "from langchain.document_loaders import JSONLoader\n", "from langchain.schema import Document\n", "\n", "# 定义JSON文件路径\n", "json_file_path = \"contract.json\"\n", "\n", "# 修改jq_schema以提取page_content为字符串\n", "jq_schema = \". | tostring\"  # 将整个JSON对象转换为字符串\n", "\n", "# 加载JSON文件\n", "loader = JSONLoader(file_path=json_file_path, jq_schema=jq_schema)\n", "data_docs = loader.load()\n", "\n", "# 遍历并打印加载的文档内容\n", "for doc in data_docs:\n", "    print(f\"Metadata: {doc.metadata}\")\n", "    print(f\"Content: {doc.page_content}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "ab0ca49c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["根据提供的电子数据和文本数据，以下是合同关键信息的一致性审核结果：\n", "\n", "---\n", "\n", "### **1. 合同编号**\n", "- **电子数据**：商财采【2015】402号  \n", "- **文本数据**：未明确填写（合同编号字段为空）  \n", "- **结论**：**不一致**（文本数据缺失编号，需补充确认）。\n", "\n", "---\n", "\n", "### **2. 合同签署日期**\n", "- **电子数据**：2015-12-15  \n", "- **文本数据**：未明确提及（仅显示“合同签订局7日内”，无具体日期）  \n", "- **结论**：**不一致**（文本数据未提供签署日期，需补充）。\n", "\n", "---\n", "\n", "### **3. 合同金额**\n", "- **电子数据**：1608600万元（需注意单位是否为“万元”，可能存在笔误）  \n", "- **文本数据**：合计金额为“壹佰陆拾万捌仟陆佰元整（¥1608600.00）”（明确为160.86万元）  \n", "- **差异分析**：  \n", "  - 若电子数据单位为“元”，则金额一致（1608600元 = 160.86万元）；  \n", "  - 若电子数据确为“1608600万元”（即160.86亿元），则与文本数据严重不符。  \n", "- **结论**：**需确认单位**（电子数据可能存在单位错误，建议核实）。\n", "\n", "---\n", "\n", "### **4. 合同乙方**\n", "- **电子数据**：烟台海德专用汽车有限公司  \n", "- **文本数据**：出卖人为“烟台海德专用汽车有限公司”  \n", "- **结论**：**一致**。\n", "\n", "---\n", "\n", "### **其他发现**\n", "- **合同名称**：电子数据与文本数据均为“产品销售合同”，一致。  \n", "- **甲方（采购人）**：电子数据与文本数据均为“商丘市环境卫生管理处”，一致。  \n", "- **金额大写**：文本数据中“壹佰陆拾万捌仟陆佰元整”与数字金额¥1608600.00匹配，无歧义。\n", "\n", "---\n", "\n", "### **审核建议**\n", "1. **合同编号**：要求补充文本数据中的合同编号字段。  \n", "2. **签署日期**：需在文本中补充具体签署日期（如“2015年12月15日”）。  \n", "3. **金额单位**：核实电子数据中“1608600万元”是否为笔误，建议修正为“1608600元”或“160.86万元”。  \n", "4. **其他条款**：文本数据中的产品明细（如扫路车数量、单价）与电子数据无冲突，但电子数据未提供明细，需确认是否需补充。\n", "\n", "**最终结论**：当前数据存在关键字段不一致或缺失，需进一步补充和修正。\n"]}], "source": ["#启动chain\n", "output=chain.invoke({'contract_base':data_docs[0].page_content,'contract_paper':scan_docs[0].page_content})\n", "print(output.content)"]}, {"cell_type": "code", "execution_count": null, "id": "c7ecb04f-c0d4-4f4e-ada2-413f22bb4c6a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}