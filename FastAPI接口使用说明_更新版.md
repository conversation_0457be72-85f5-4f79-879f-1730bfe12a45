# 分光器预测 FastAPI 接口使用说明（更新版）

## 概述

`splitter_prediction_fastapi.py` 已按照方案一进行优化，将趋势分析和风险评估集成到预测接口中，现在只保留3个核心接口。

## 核心改进

### 🔄 **架构简化**
- **删除独立接口**：移除了 `/api/analyze/trend`、`/api/analyze/risk`、`/api/config` 接口
- **功能集成**：趋势分析和风险评估现在作为预测结果的一部分返回
- **减少调用**：Java后端只需调用预测接口，即可获得完整的分析结果

### 📊 **数据增强**
- **单设备预测**：增加了 `trendData` 和 `riskAssessment` 字段
- **批量预测**：增加了 `riskLevel` 和 `riskScore` 字段
- **一次调用**：获得预测、趋势、风险的完整信息

## API 接口详情

### 1. 健康检查接口

**接口地址：** `GET /api/health`

**功能：** 检查API服务是否正常运行

**响应示例：**
```json
{
    "status": "healthy",
    "service": "splitter-prediction-api",
    "version": "2.0.0",
    "tensorflow_available": true,
    "timestamp": "2024-12-25T10:30:00"
}
```

### 2. 单设备预测接口（增强版）

**接口地址：** `POST /api/predict/single`

**功能：** 对单个设备进行预测分析，包含趋势分析和风险评估

**请求模型：** `SinglePredictionRequest`（无变化）
```json
{
    "deviceCode": "CZ-FG-001",
    "period": "week",
    "historicalData": [
        {
            "实占率": 0.85,
            "空闲端口数": 8,
            "分光器容量": 48,
            "潜在需求比": 0.6,
            "ftth终端数": 120,
            "覆盖的工程级的线路到达房间数": 200,
            "容量变化": 0
        }
    ]
}
```

**响应模型：** `SinglePredictionResponse`（增强版）
```json
{
    "hasData": true,
    "设备编码": "CZ-FG-001",
    "prediction": {
        "nextWeekRate": 87.5,
        "nextMonthRate": 89.2,
        "trend": "increasing",
        "confidence": 85.0
    },
    "recommendations": {
        "expansionAdvice": "建议在2周内增加分光器容量",
        "adjustmentAdvice": "可考虑将部分用户迁移至邻近设备",
        "priority": "medium"
    },
    "historicalData": [...],
    "deviceType": "normal",
    "trendAnalysis": "📈 业务增长趋势 | 🎯 实占率85.0% | ✅ 运行正常",
    
    // 🆕 新增：趋势数据（替代原 /api/analyze/trend 接口）
    "trendData": [
        {"week": "第1周", "occupancyRate": 85.2},
        {"week": "第2周", "occupancyRate": 86.1},
        {"week": "第3周", "occupancyRate": 87.0}
    ],
    
    // 🆕 新增：风险评估（替代原 /api/analyze/risk 接口）
    "riskAssessment": {
        "riskScore": 75.5,
        "riskLevel": "medium",
        "reasons": ["实占率较高（≥ 80%）", "空闲端口数较少（4-5个）"],
        "recommendations": "建议制定扩容计划，定期检查设备负载"
    }
}
```

### 3. 批量预测接口（增强版）

**接口地址：** `POST /api/predict/batch`

**功能：** 批量预测多个设备，包含风险评估信息

**请求模型：** `BatchPredictionRequest`（无变化）
```json
{
    "devices": [
        {
            "deviceCode": "CZ-FG-001",
            "historicalData": [...]
        },
        {
            "deviceCode": "CZ-FG-002",
            "historicalData": [...]
        }
    ]
}
```

**响应模型：** `List[BatchPredictionItem]`（增强版）
```json
[
    {
        "设备编码": "CZ-FG-001",
        "预测实占率": 87.5,
        "预测空闲数": 6,
        "预测状态": "attention",
        "deviceType": "stable",
        
        // 🆕 新增：风险评估信息
        "riskLevel": "medium",
        "riskScore": 75.5
    },
    {
        "设备编码": "CZ-FG-002",
        "预测实占率": 95.2,
        "预测空闲数": 2,
        "预测状态": "alarm",
        "deviceType": "volatile",
        
        // 🆕 新增：风险评估信息
        "riskLevel": "high",
        "riskScore": 92.3
    }
]
```

## 与 Java 后端的对接变化

### 原来的调用方式（多个接口）：
```java
// 原来需要多次调用
JSONObject prediction = callPythonAPI("/api/predict/single", request);
JSONObject trendData = callPythonAPI("/api/analyze/trend", trendRequest);
JSONObject riskData = callPythonAPI("/api/analyze/risk", riskRequest);

// 手动组合结果
JSONObject result = combinePredictionResults(prediction, trendData, riskData);
```

### 现在的调用方式（单个接口）：
```java
// 现在只需一次调用，获得完整结果
JSONObject completeResult = callPythonAPI("/api/predict/single", request);

// 直接提取各部分数据
JSONObject prediction = completeResult.getJSONObject("prediction");
JSONArray trendData = completeResult.getJSONArray("trendData");
JSONObject riskAssessment = completeResult.getJSONObject("riskAssessment");
```

### Java 后端需要修改的方法：

#### 1. SplitterServiceImpl.getTrendData()
```java
// 修改前：调用独立的趋势分析接口
@Override
public List<JSONObject> getTrendData(JSONObject params) {
    String response = httpClient.post("http://localhost:8000/api/analyze/trend", params);
    return parseResponse(response);
}

// 修改后：从预测结果中提取趋势数据
@Override
public List<JSONObject> getTrendData(JSONObject params) {
    // 调用单设备预测接口
    JSONObject predictionResult = predictSingleDevice(params);
    
    // 提取趋势数据
    JSONArray trendData = predictionResult.getJSONArray("trendData");
    return trendData.toJavaList(JSONObject.class);
}
```

#### 2. SplitterServiceImpl.calculateRiskAssessment()
```java
// 修改前：调用独立的风险评估接口
@Override
public JSONObject calculateRiskAssessment(String deviceCode) {
    JSONObject request = buildRiskRequest(deviceCode);
    String response = httpClient.post("http://localhost:8000/api/analyze/risk", request);
    return parseResponse(response);
}

// 修改后：从预测结果中提取风险评估
@Override
public JSONObject calculateRiskAssessment(String deviceCode) {
    // 调用单设备预测接口
    JSONObject predictionRequest = buildPredictionRequest(deviceCode);
    JSONObject predictionResult = predictSingleDevice(predictionRequest);
    
    // 提取风险评估
    return predictionResult.getJSONObject("riskAssessment");
}
```

#### 3. SplitterServiceImpl.getStatisticsDictionary()
```java
// 修改前：调用配置接口
@Override
public JSONObject getStatisticsDictionary(JSONObject params) {
    String response = httpClient.get("http://localhost:8000/api/config");
    JSONObject config = parseResponse(response);
    return mergeDictionaryData(config);
}

// 修改后：在Java中管理配置信息
@Override
public JSONObject getStatisticsDictionary(JSONObject params) {
    JSONObject dictionary = new JSONObject();
    
    // 硬编码配置信息（原来从Python获取）
    JSONObject healthThresholds = new JSONObject();
    healthThresholds.put("attention", 80);
    healthThresholds.put("alarm", 90);
    dictionary.put("healthThresholds", healthThresholds);
    
    // 从数据库获取其他字典数据
    dictionary.put("regions", splitterDao.queryRegions(new JSONObject(), NRMConstants.SHARDING_CODE));
    dictionary.put("statusList", createStatusList());
    
    return dictionary;
}
```

## 优势总结

### 🚀 **性能提升**
- **减少网络调用**：从3次HTTP请求减少到1次
- **降低延迟**：减少网络往返时间
- **提高吞吐量**：减少连接开销

### 🔧 **维护简化**
- **接口数量减少**：从6个接口减少到3个
- **依赖关系简化**：Java后端对Python服务的依赖更清晰
- **错误处理简化**：只需处理一个接口的异常情况

### 📊 **数据一致性**
- **原子操作**：预测、趋势、风险基于同一份数据计算
- **时间一致性**：避免多次调用时数据状态不一致
- **逻辑一致性**：所有分析基于相同的算法版本

这样的优化使整个系统更加高效、简洁和可靠！
