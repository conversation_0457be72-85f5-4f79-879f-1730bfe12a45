{"cells": [{"cell_type": "markdown", "id": "6b9a39e8-2a95-4e1d-aac5-4a4dd4b878f8", "metadata": {}, "source": ["## 练习4：工作周报的生成的图\n", "\n", "**START**\n", "\n", "**输入**：本周的工作计划 & 本周的工作进展\n", "\n", "**节点1**：根据工作列表生成一个上周的工作周报（格式）。\n", "\n", "**节点2**：根据上周的工作周报，生成下周的工作计划\n", "\n", "**END**"]}, {"cell_type": "code", "execution_count": 1, "id": "4a57d87b-d6a4-445f-85d1-02f3b28be4d5", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "#创建一个和大模型的连接器\n", "url='https://api.deepseek.com'\n", "llm=ChatOpenAI(model='deepseek-chat',base_url=url, api_key=os.environ[\"DS_API_KEY\"])"]}, {"cell_type": "code", "execution_count": 2, "id": "19261fa1-5aad-487e-a482-d46d0d984246", "metadata": {}, "outputs": [], "source": ["from langchain.prompts import ChatPromptTemplate\n", "prompt1_str='''你是一个周报小助手，你要完成以下任务：\n", "1.对照我提供的工作计划和工作进展，分析哪些任务已经完成。哪些任务没有完成。\n", "2.对于已经完成的任务，写出详细的周报内容。\n", "3.对于没有完成的任务，写出没完成的原因（可以编写）\n", "4.输出周报内容，不要解释，不要说明。\n", "本周的工作计划：{plan}, 本周的工作进展{work}'''\n", "prompt2_str='''根据本周的周报，写出下周的计划。本周的周报：{weekly_report}'''\n", "node1_prompt_template=ChatPromptTemplate.from_template(prompt1_str)\n", "node2_prompt_template=ChatPromptTemplate.from_template(prompt2_str)\n", "\n", "chain1=node1_prompt_template | llm\n", "chain2=node2_prompt_template | llm"]}, {"cell_type": "code", "execution_count": 3, "id": "cd6b6e79-88eb-42c4-b683-b64d4da0cb33", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict #指定字典格式\n", "from typing import Literal #更严格的类型检查方式\n", "\n", "class TypedDictState(TypedDict): #此类对应的实例就是State\n", "    plan: str\n", "    work: str\n", "    weekly_report: str\n", "    next_plan: str"]}, {"cell_type": "code", "execution_count": 4, "id": "1db868cb-ada5-4248-9ec2-ed2bb6dd6358", "metadata": {}, "outputs": [], "source": ["#设计Graph\n", "from langgraph.graph import StateGraph, START, END\n", "from IPython.display import Image, display\n", "def node_1(state):\n", "    print(\"---Node 1 写本周周报---\")\n", "    return {\"weekly_report\": chain1.invoke({\"plan\":state['plan'],\"work\":state['work']}).content}\n", "\n", "def node_2(state):\n", "    print(\"---Node 2 写下周的计划---\")\n", "    return {\"next_plan\": chain2.invoke(state['weekly_report']).content}\n", "\n", "\n", "# Build graph\n", "builder = StateGraph(TypedDictState)\n", "builder.add_node(\"node_1\", node_1)\n", "builder.add_node(\"node_2\", node_2)\n", "\n", "# Logic\n", "builder.add_edge(START, \"node_1\")\n", "builder.add_edge(\"node_1\",\"node_2\")\n", "builder.add_edge(\"node_2\", END)\n", "\n", "# Add\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 6, "id": "9dc9f587-73fc-47d1-833d-1ecc81089e91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Node 1 写本周周报---\n", "---Node 2 写下周的计划---\n", "---本周的周报-----\n", "# 周报\n", "\n", "## 已完成任务\n", "\n", "1. **拜访客户C**  \n", "   周二成功拜访客户C，讨论了合作细节和后续计划。\n", "\n", "2. **完成B的报表**  \n", "   周一完成了报表B的设计工作，已提交给相关部门审核。\n", "\n", "3. **完成AI的培训**  \n", "   周三至周五参加了AI培训课程，掌握了相关知识和技能。\n", "\n", "## 未完成任务\n", "\n", "1. **拜访客户A**  \n", "   周二计划拜访客户A，但因客户临时有事未能如期进行，已重新预约下周拜访时间。\n", "----下周的计划-----\n", "# 下周工作计划\n", "\n", "## 客户拜访安排\n", "1. **拜访客户A**（优先级高）\n", "   - 按照重新预约的时间（周二）完成拜访\n", "   - 提前准备合作方案和演示材料\n", "   - 跟进拜访后的合作意向确认\n", "\n", "2. **客户C后续跟进**\n", "   - 整理上周拜访的会议纪要\n", "   - 发送合作细节确认邮件\n", "   - 安排下一步合作实施计划\n", "\n", "## 报表相关工作\n", "1. **报表B的反馈处理**\n", "   - 跟进审核部门的反馈意见\n", "   - 根据反馈进行必要的修改\n", "   - 完成最终版本并归档\n", "\n", "2. **新报表规划**\n", "   - 开始设计报表D的框架\n", "   - 收集相关部门的数据需求\n", "\n", "## AI技能应用\n", "1. **实践AI培训所学**\n", "   - 在工作流程中尝试应用1-2个新学到的AI工具\n", "   - 记录应用效果和改进建议\n", "\n", "2. **知识分享**\n", "   - 准备简单的AI应用案例分享\n", "   - 安排团队内部小型分享会\n", "\n", "## 其他事项\n", "1. **周中进度检查**\n", "   - 周三下午进行本周工作进度评估\n", "   - 根据实际情况调整后续计划\n", "\n", "2. **学习与发展**\n", "   - 每天安排30分钟AI相关知识巩固学习\n", "   - 收集下周可能的培训机会信息\n", "\n", "## 注意事项\n", "- 确保客户拜访前的充分准备\n", "- 及时处理报表B的反馈以避免项目延误\n", "- 平衡新技能学习与实际工作应用的时间分配\n"]}], "source": ["plan='1.拜访客户A， 2.完成B的报表  3. 拜访客户C   4. 完成AI的培训'\n", "work='周一，我完成了报表B的设计。 周二，我去拜访客户A，但是客户临时有事。 于是我去拜访客户C，成功拜访。 周三到周五，参加了AI的培训'\n", "output_dct=graph.invoke({'plan':plan,'work':work})\n", "print('---本周的周报-----')\n", "print(output_dct['weekly_report'])\n", "print('----下周的计划-----')\n", "print(output_dct['next_plan'])"]}, {"cell_type": "code", "execution_count": null, "id": "dfb67498-4d1a-42ab-bd2c-03f8fc0d5b22", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}