{"cells": [{"cell_type": "code", "execution_count": 1, "id": "58d8ad4f-d720-4537-a78b-eb44991f27b0", "metadata": {}, "outputs": [], "source": ["#pip install graph_rag_example_helpers\n", "#pip install langchain_graph_retriever\n", "#pip install networkx\n", "\n", "from langchain_chroma.vectorstores import Chroma\n", "from langchain_graph_retriever.transformers import ShreddingTransformer\n", "from langchain_community.embeddings import OllamaEmbeddings\n", "from langchain_community.vectorstores.utils import filter_complex_metadata  # Import utility\n", "from graph_rag_example_helpers.datasets.animals import fetch_documents\n", "import json\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "08f9ca1c-0efd-4c54-bf51-96e8b7ce25dd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18972\\127558995.py:5: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  json.dump([doc.dict() for doc in animals], f, ensure_ascii=False, indent=4)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18972\\127558995.py:15: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  json.dump([doc.dict() for doc in filtered_documents], f, ensure_ascii=False, indent=4)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18972\\127558995.py:23: LangChainDeprecationWarning: The class `OllamaEmbeddings` was deprecated in LangChain 0.3.1 and will be removed in 1.0.0. An updated version of the class exists in the :class:`~langchain-ollama package and should be used instead. To use it run `pip install -U :class:`~langchain-ollama` and import as `from :class:`~langchain_ollama import OllamaEmbeddings``.\n", "  embedding=OllamaEmbeddings(model='all-minilm'),\n"]}], "source": ["#第一次需要从网页下载document，之后不需要了\n", "animals = fetch_documents()\n", "# Save animals to a local JSON file\n", "with open(\"animals.json\", \"w\", encoding=\"utf-8\") as f:\n", "    json.dump([doc.dict() for doc in animals], f, ensure_ascii=False, indent=4)\n", "\n", "\n", "\n", "# Filter complex metadata\n", "filtered_documents = filter_complex_metadata(\n", "    list(ShreddingTransformer().transform_documents(animals))\n", ")\n", "# Save filtered documents to disk\n", "with open(\"filtered_animals.json\", \"w\", encoding=\"utf-8\") as f:\n", "    json.dump([doc.dict() for doc in filtered_documents], f, ensure_ascii=False, indent=4)\n", "\n", "\n", "\n", "#创建向量数据库，保存到本地\n", "# Create a Chroma vector store with the filtered documents\n", "vector_store = Chroma.from_documents(\n", "    documents=filtered_documents,\n", "    embedding=OllamaEmbeddings(model='all-minilm'),\n", "    collection_name=\"animals\",\n", "    persist_directory='./animals_chroma',\n", ")"]}, {"cell_type": "markdown", "id": "037b2e0f", "metadata": {}, "source": ["## filter_complex_metadata 会移除文档中的复杂元数据字段。\n", "\n", "{\n", "    \"id\": \"aardvark\",\n", "    \"metadata\": {\n", "        \"type\": \"mammal\",\n", "        \"number_of_legs\": 4,\n", "        \n", "        \"keywords\": [\"burrowing\", \"nocturnal\", \"ants\"],\n", "        \n", "        \"habitat\": \"savanna\",\n", "        \n", "        \"tags\": [\n", "            {\"a\": 5, \"b\": 7},\n", "            {\"a\": 8, \"b\": 10}\n", "            \n", "        ]\n", "    },\n", "    \"page_content\": \"The aardvark is a nocturnal mammal known for its burrowing habits and long snout used to sniff out ants.\"\n", "}\n", "\n", "## 经过 filter_complex_metadata 处理后，元数据可能变为：\n", "\n", "{\n", "    \"id\": \"aardvark\",\n", "    \"metadata\": {\n", "        \"type\": \"mammal\",\n", "        \"number_of_legs\": 4,\n", "        \n", "        \"keywords→\\\"burrowing\\\"\": \"§\",\n", "        \n", "        \"keywords→\\\"nocturnal\\\"\": \"§\",\n", "        \n", "        \"keywords→\\\"ants\\\"\": \"§\",\n", "        \n", "        \"habitat\": \"savanna\",\n", "        \n", "        \"tags→{\\\"a\\\": 5, \\\"b\\\": 7}\": \"§\",\n", "        \n", "        \"tags→{\\\"a\\\": 8, \\\"b\\\": 10}\": \"§\",\n", "        \n", "        \"__shredded_keys\": [\"keywords\", \"tags\"]\n", "    },\n", "    \"page_content\": \"The aardvark is a nocturnal mammal known for its burrowing habits and long snout used to sniff out ants.\"\n", "}"]}, {"cell_type": "markdown", "id": "9477862d", "metadata": {}, "source": ["ShreddingTransformer 是一个用于将文档分解为更小单元的工具类，通常用于图检索或向量化任务中。以下是其详细解释：\n", "\n", "功能\n", "ShreddingTransformer 的主要功能是将复杂的文档结构“切碎”成更小的片段或单元。这些片段可以是文档的子部分、段落、句子，甚至是特定的元数据字段。通过这种方式，可以更细粒度地处理文档内容。\n", "\n", "使用场景\n", "\n", "图检索：在构建图结构时，将文档分解为节点，每个节点代表文档的一部分。\n", "向量化：将文档的每个片段单独向量化，以便更精确地进行相似性搜索。\n", "复杂元数据处理：提取文档中的复杂元数据并将其转化为独立的结构化数据。\n", "\n", "原始文档\n", "\n", "{\n", "\"id\": \"土豚\",\n", "\"metadata\": {\n", "\"type\": \"哺乳动物\",\n", "\"number_of_legs\": 4,\n", "\"keywords\": [\n", "\"穴居\",\n", "\"夜行性\",\n", "\"蚂蚁\",\n", "\"稀树草原\"\n", "],\n", "\"habitat\": \"稀树草原\",\n", "\"tags\": [\n", "{\n", "\"a\": 5,\n", "\"b\": 7\n", "},\n", "{\n", "\"a\": 8,\n", "\"b\": 10\n", "}\n", "]\n", "},\n", "\"page_content\": \"土豚是一种夜行性哺乳动物，以其穴居习性和用于嗅出蚂蚁的长鼻子而闻名。\",\n", "\"type\": \"文档\"\n", "},\n", "\n", "过滤后\n", "\n", "{\n", "\"id\": \"土豚\",\n", "\"metadata\": {\n", "\"type\": \"哺乳动物\",\n", "\"number_of_legs\": 4,\n", "\"keywords→\\\"挖洞\\\"\": \"§\",\n", "\"keywords→\\\"夜行\\\"\": \"§\",\n", "\"keywords→\\\"蚂蚁\\\"\": \"§\",\n", "\"keywords→\\\"热带草原\\\"\": \"§\",\n", "\"habitat\": \"热带草原\",\n", "\"tags→{\\\"a\\\": 5, \\\"b\\\": 7}\": \"§\",\n", "\"tags→{\\\"a\\\": 8, \\\"b\\\": 10}\": \"§\",\n", "\"__shredded_keys\": \"keywords\\\", \"tags\\\"]\"\n", "},\n", "\"page_content\": \"土豚是一种夜行性哺乳动物，以其挖洞习性和用于嗅出蚂蚁的长鼻子而闻名。\",\n", "\"type\": \"Document\"\n", "},"]}, {"cell_type": "code", "execution_count": 3, "id": "3dc94ded-44d9-4099-9b3e-e64bf3b74fe5", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 第二次使用的时候，不用再下载文档了。直接使用即可。\n", "with open(\"animals.json\", \"r\", encoding=\"utf-8\") as f:\n", "    animals = json.load(f)\n", "# Load filtered documents from the local JSON file\n", "with open(\"filtered_animals.json\", \"r\", encoding=\"utf-8\") as f:\n", "    filtered_documents = json.load(f)\n", "# Load the vector store from disk\n", "vector_store = Chroma(\n", "    embedding_function=OllamaEmbeddings(model='all-minilm'),\n", "    collection_name=\"animals\",\n", "    persist_directory='./animals_chroma',\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "aa21cc3d-b1b5-4293-8be1-f83ecb9743c7", "metadata": {}, "outputs": [], "source": ["from graph_retriever.strategies import Eager\n", "from langchain_graph_retriever import GraphRetriever\n", "\n", "#traversal 遍历召回器\n", "traversal_retriever = GraphRetriever(\n", "    store = vector_store, # 图数据存储的向量数据库\n", "    edges = [(\"habitat\", \"habitat\"), (\"origin\", \"origin\")], # 定义图的边，(\"habitat\", \"habitat\") 表示基于栖息地的连接\n", "    strategy = Eager(k=5, start_k=1, max_depth=2), # 使用 Eager 策略\n", ")"]}, {"cell_type": "markdown", "id": "3eb7f3e0", "metadata": {}, "source": ["Eager 是一种用于图检索的策略类，通常用于控制图的遍历方式。\n", "\n", "Eager 的参数\n", "Eager 类的构造函数接受多个参数，用于定义图遍历的行为：\n", "\n", "k：每次检索时返回的节点数量。\n", "start_k：初始检索时返回的节点数量。\n", "max_depth：图遍历的最大深度，控制检索的范围。"]}, {"cell_type": "code", "execution_count": 5, "id": "f5525cb0-183c-42e3-858e-68fad0e4013e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["capybara: capybaras are the largest rodents in the world and are highly social animals.\n", "newt: newts are small amphibians known for their ability to regenerate limbs and tails.\n", "crocodile: crocodiles are large reptiles with powerful jaws and a long lifespan, often living over 70 years.\n", "duck: ducks are waterfowl birds known for their webbed feet and quacking sounds.\n", "frog: frogs are amphibians known for their jumping ability and croaking sounds.\n"]}], "source": ["#提问：水豚附近有哪些动物？\n", "results = traversal_retriever.invoke(\"what animals could be found near a capybara?\")\n", "\n", "for doc in results:\n", "    print(f\"{doc.id}: {doc.page_content}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "affb847d-1cb6-4530-aac4-c5179807319f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["antelope: antelopes are graceful herbivorous mammals that are often prey for large predators in the wild.\n", "buffalo: buffalo are large herbivorous mammals known for their horns and grazing habits.\n", "coyote: coyotes are adaptable mammals known for their distinctive howls and cunning nature.\n", "hedgehog: hedgehogs are small mammals known for their spiky quills and ability to curl into a ball.\n"]}], "source": ["#提问：羚羊附近有哪些动物？\n", "results = traversal_retriever.invoke(\"what animals could be found near a antelope?\")\n", "\n", "for doc in results:\n", "    print(f\"{doc.id}: {doc.page_content}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "97e49e54-4584-4fcc-a4d6-d5d99ba39c65", "metadata": {}, "outputs": [], "source": ["#传统的RAG召回器\n", "standard_retriever = GraphRetriever(\n", "    store = vector_store,\n", "    edges = [(\"habitat\", \"habitat\"), (\"origin\", \"origin\")],\n", "    strategy = Eager(k=5, start_k=5, max_depth=0),\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "8fc403c7-cb1a-46ad-9ac0-d5390f0e0061", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["capybara: capybaras are the largest rodents in the world and are highly social animals.\n", "giraffe: giraffes are the tallest land animals, known for their long necks and unique spotted patterns.\n", "bear: bears are large mammals that can be found across many habitats, from forests to tundras.\n", "elephant: elephants are the largest land mammals, known for their intelligence and strong social bonds.\n", "gazelle: gazelles are fast and agile mammals commonly found in grasslands and savannas.\n"]}], "source": ["#提问：水豚附近有哪些动物？\n", "results = standard_retriever.invoke(\"what animals could be found near a capybara?\")\n", "\n", "for doc in results:\n", "    print(f\"{doc.id}: {doc.page_content}\")"]}, {"cell_type": "code", "execution_count": 9, "id": "1eebb74a-aa4a-4f1a-a9dc-d54128a5859c", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}, "scrolled": true}, "outputs": [{"data": {"text/plain": ["'import pandas as pd\\n\\n# Extract relevant fields from the animals list\\nprocessed_data = [\\n    {\\n        \"id\": animal[\"id\"],\\n        \"type\": animal[\"metadata\"].get(\"type\", \"\"),\\n        \"keywords\": \", \".join(animal[\"metadata\"].get(\"keywords\", [])),\\n        \"habitat\": animal[\"metadata\"].get(\"habitat\", \"\"),\\n        \"page_content\": animal[\"page_content\"],\\n    }\\n    for animal in animals\\n]\\n\\n# Create a DataFrame from the processed data\\nanimals_df = pd.DataFrame(processed_data)\\n\\n# Save the DataFrame to a CSV file\\nanimals_df.to_csv(\"animals.csv\", index=False, encoding=\"utf-8-sig\")\\n# Load the CSV file into a DataFrame\\n#animals_df = pd.read_csv(\"animals.csv\", encoding=\"utf-8-sig\")\\n#print(animals)'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#把animals中的id,type,keywords,habitat,page_content字段保存到一个新的csv文件中\n", "'''import pandas as pd\n", "\n", "# Extract relevant fields from the animals list\n", "processed_data = [\n", "    {\n", "        \"id\": animal[\"id\"],\n", "        \"type\": animal[\"metadata\"].get(\"type\", \"\"),\n", "        \"keywords\": \", \".join(animal[\"metadata\"].get(\"keywords\", [])),\n", "        \"habitat\": animal[\"metadata\"].get(\"habitat\", \"\"),\n", "        \"page_content\": animal[\"page_content\"],\n", "    }\n", "    for animal in animals\n", "]\n", "\n", "# Create a DataFrame from the processed data\n", "animals_df = pd.DataFrame(processed_data)\n", "\n", "# Save the DataFrame to a CSV file\n", "animals_df.to_csv(\"animals.csv\", index=False, encoding=\"utf-8-sig\")\n", "# Load the CSV file into a DataFrame\n", "#animals_df = pd.read_csv(\"animals.csv\", encoding=\"utf-8-sig\")\n", "#print(animals)'''"]}, {"cell_type": "code", "execution_count": null, "id": "8f0ef8eb-31f2-4e70-8383-4f5d2d813911", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}