{"cells": [{"cell_type": "markdown", "id": "834061f0", "metadata": {}, "source": ["# Lesson 3: Connecting to a SQL Database"]}, {"cell_type": "markdown", "id": "34cb8641", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 1, "id": "f50bdee1-25c0-472e-a739-c8551b5c5349", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T08:51:01.018389Z", "start_time": "2025-04-15T08:51:00.977381Z"}, "height": 132}, "outputs": [], "source": ["import os\n", "from IPython.display import Markdown, HTML, display\n", "from langchain.agents import create_sql_agent\n", "from langchain.agents.agent_toolkits import SQLDatabaseToolkit\n", "from langchain.sql_database import SQLDatabase\n", "from sqlalchemy import create_engine\n", "import pandas as pd\n", "deepseek_key='***********************************'"]}, {"cell_type": "markdown", "id": "8df5c4c1", "metadata": {}, "source": ["## 把excel数据导入 SQL database"]}, {"cell_type": "code", "execution_count": 2, "id": "6bb39f31-4ab3-4c95-a55d-4140fd68711f", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T22:24:11.689775Z", "start_time": "2025-04-14T22:24:11.672764Z"}, "height": 251}, "outputs": [], "source": ["# 创建数据库\n", "database_file_path = \"company.db\"\n", "engine = create_engine(f'sqlite:///{database_file_path}')\n"]}, {"cell_type": "code", "execution_count": 17, "id": "4ff36d4e", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T22:44:10.284368Z", "start_time": "2025-04-14T22:44:10.214244Z"}}, "outputs": [], "source": ["#把excel导入到数据库中\n", "#for file_url in  [\"员工考勤分析\",'月度费用预算及分析统计表','员工考勤分析','员工工资表','员工信息','电商客户反馈']:\n", "#    df = pd.read_excel(file_url+'.xlsx').fillna(value = 0)\n", "#    df.to_sql(\n", "#        file_url,\n", "#        con=engine,\n", "#        if_exists='replace',\n", "#        index=False\n", "#    )"]}, {"cell_type": "markdown", "id": "8307ac30", "metadata": {}, "source": ["##  SQL 提示词"]}, {"cell_type": "code", "execution_count": 2, "id": "a2e02e43-c499-4897-9981-78e7fbae3491", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T08:51:04.584272Z", "start_time": "2025-04-15T08:51:04.570274Z"}, "height": 608}, "outputs": [], "source": ["MSSQL_AGENT_PREFIX = \"\"\"\n", "\n", "You are an agent designed to interact with a SQL database.\n", "## Instructions:\n", "- Given an input question, create a syntactically correct {dialect} query\n", "to run, then look at the results of the query and return the answer.\n", "- Unless the user specifies a specific number of examples they wish to\n", "obtain, **ALWAYS** limit your query to at most {top_k} results.\n", "- You can order the results by a relevant column to return the most\n", "interesting examples in the database.\n", "- Never query for all the columns from a specific table, only ask for\n", "the relevant columns given the question.\n", "- You have access to tools for interacting with the database.\n", "- You MUST double check your query before executing it.If you get an error\n", "while executing a query,rewrite the query and try again.\n", "- DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.)\n", "to the database.\n", "- DO NOT MAKE UP AN ANSWER OR USE PRIOR KN<PERSON>LEDGE, ONLY USE THE RESULTS\n", "OF THE CALCULATIONS YOU HAVE DONE.\n", "- Your response should be in Markdown. However, **when running  a SQL Query\n", "in \"Action Input\", do not include the markdown backticks**.\n", "Those are only for formatting the response, not for executing the command.\n", "- ALWAYS, as part of your final answer, explain how you got to the answer\n", "on a section that starts with: \"Explanation:\". Include the SQL query as\n", "part of the explanation section.\n", "- If the question does not seem related to the database, just return\n", "\"I don\\'t know\" as the answer.\n", "- Only use the below tools. Only use the information returned by the\n", "below tools to construct your query and final answer.\n", "- Do not make up table names, only use the tables returned by any of the\n", "tools below.\n", "\n", "## Tools:\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "id": "bbb64505", "metadata": {}, "source": ["您是设计用于与 SQL 数据库交互的代理。\n", "## 说明：\n", "- 给定一个输入问题，创建一个语法正确的 {dialect} 查询\n", "来运行，然后查看查询结果并返回答案。\n", "- 除非用户指定他们希望获得的特定示例数量，否则**始终**将查询限制为最多 {top_k} 个结果。\n", "- 您可以按相关列对结果进行排序，以返回数据库中最有趣的示例。\n", "- 切勿查询特定表中的所有列，只询问给定问题的相关列。\n", "- 您可以使用与数据库交互的工具。\n", "- 执行查询之前，您必须仔细检查查询。如果在执行查询时出现错误，请重写查询并重试。\n", "- 请勿对数据库执行任何 DML 语句（INSERT、UPDATE、DELETE、DROP 等）。\n", "- 不要编造答案或使用先前的知识，仅使用您已完成的计算的结果。\n", "- 您的回复应使用 Markdown。但是，**在“操作输入”中运行 SQL 查询时，请勿包含 markdown 反引号**。\n", "这些仅用于格式化响应，而不是用于执行命令。\n", "- 始终作为最终答案的一部分，在以“解释：”开头的部分解释您如何得到答案。将 SQL 查询作为解释部分的一部分。\n", "- 如果问题似乎与数据库无关，只需返回“我不知道”作为答案。\n", "- 仅使用以下工具。仅使用以下工具返回的信息来构建查询和最终答案。\n", "- 不要编造表名，仅使用以下任何工具返回的表。\n", "\n", "## 工具："]}, {"cell_type": "code", "execution_count": 3, "id": "e70cd29a-c369-4419-ae27-e1f9912b5064", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T08:51:07.633368Z", "start_time": "2025-04-15T08:51:07.628355Z"}, "height": 693}, "outputs": [], "source": ["MSSQL_AGENT_FORMAT_INSTRUCTIONS = \"\"\"\n", "\n", "## Use the following format:\n", "\n", "Question: the input question you must answer.\n", "Thought: you should always think about what to do.\n", "Action: the action to take, should be one of [{tool_names}].\n", "Action Input: the input to the action.\n", "Observation: the result of the action.\n", "... (this Thought/Action/Action Input/Observation can repeat N times)\n", "Thought: I now know the final answer.\n", "Final Answer: the final answer to the original input question.\n", "\n", "Example of Final Answer:\n", "<=== Beginning of example\n", "\n", "Action: query_sql_db\n", "Action Input: \n", "SELECT TOP (10) [death]\n", "FROM covidtracking \n", "WHERE state = 'TX' AND date LIKE '2020%'\n", "\n", "Observation:\n", "[(27437.0,), (27088.0,), (26762.0,), (26521.0,), (26472.0,), (26421.0,), (26408.0,)]\n", "Thought:I now know the final answer\n", "Final Answer: There were 27437 people who died of covid in Texas in 2020.\n", "\n", "Explanation:\n", "I queried the `covidtracking` table for the `death` column where the state\n", "is 'TX' and the date starts with '2020'. The query returned a list of tuples\n", "with the number of deaths for each day in 2020. To answer the question,\n", "I took the sum of all the deaths in the list, which is 27437.\n", "I used the following query\n", "\n", "```sql\n", "SELECT [death] FROM covidtracking WHERE state = 'TX' AND date LIKE '2020%'\"\n", "```\n", "===> End of Example\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "id": "6e2bde5d", "metadata": {}, "source": ["## 使用以下格式：\n", "\n", "问题：您必须回答的输入问题。\n", "想法：您应该始终思考要做什么。\n", "行动：要采取的行动，应该是 [{tool_names}] 之一。\n", "行动输入：行动的输入。\n", "观察：行动的结果。\n", "...（此想法/行动/行动输入/观察可以重复 N 次）\n", "想法：我现在知道最终答案了。\n", "最终答案：原始输入问题的最终答案。\n", "\n", "最终答案示例：\n", "<=== 示例开头\n", "\n", "操作：query_sql_db\n", "操作输入：\n", "SELECT TOP (10) [death]\n", "FROM covidtracking\n", "WHERE state = 'TX' AND date LIKE '2020%'\n", "\n", "观察：\n", "[(27437.0,), (27088.0,), (26762.0,), (26521.0,), (26472.0,), (26421.0,), (26408.0,)]\n", "想法：我现在知道最终答案了\n", "最终答案：2020 年德克萨斯州有 27437 人死于新冠肺炎。\n", "\n", "说明：\n", "我在 `covidtracking` 表中查询了 `death` 列，其中州为\n", "'TX'，日期以 '2020' 开头。该查询返回了一个元组列表，其中包含 2020 年每天的死亡人数。为了回答这个问题，我计算了列表中所有死亡人数的总和，即 27437。我使用了以下查询\n", "\n", "```sql\n", "SELECT [death] FROM covidtracking WHERE state = 'TX' AND date LIKE '2020%'\"\n", "```\n", "===> 示例结束"]}, {"cell_type": "markdown", "id": "a9350e98", "metadata": {}, "source": ["## 创建与deepseek的连接器\n", "## 创建 SQL agent"]}, {"cell_type": "code", "execution_count": 4, "id": "50e7259a-ee4b-4c8f-a0fa-f747237ad6a4", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T08:51:10.767008Z", "start_time": "2025-04-15T08:51:10.636992Z"}, "height": 234}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "#创建一个和大模型的连接器\n", "url='https://api.deepseek.com'\n", "llm=ChatOpenAI(model='deepseek-chat',base_url=url, api_key=deepseek_key)\n", "\n", "from langchain.agents.agent_toolkits import SQLDatabaseToolkit\n", "from langchain.sql_database import SQLDatabase\n", "\n", "database_file_path = \"company.db\"\n", "db = SQLDatabase.from_uri(f'sqlite:///{database_file_path}')\n", "toolkit = SQLDatabaseToolkit(db=db, llm=llm)\n", "\n", "agent_executor_SQL = create_sql_agent(\n", "    prefix=MSSQL_AGENT_PREFIX,\n", "    format_instructions = MSSQL_AGENT_FORMAT_INSTRUCTIONS,\n", "    llm=llm,\n", "    toolkit=toolkit,\n", "    top_k=30,\n", "    verbose=True\n", ")"]}, {"cell_type": "markdown", "id": "9fea48e9", "metadata": {}, "source": ["## 开始提问啦！"]}, {"cell_type": "code", "execution_count": 5, "id": "953cee48-831b-4b87-98ef-359fafb7492d", "metadata": {"ExecuteTime": {"end_time": "2025-04-15T08:51:52.778148Z", "start_time": "2025-04-15T08:51:13.519627Z"}, "height": 30}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new SQL Agent Executor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction: sql_db_list_tables\n", "Action Input: \u001b[0m\u001b[38;5;200m\u001b[1;3m员工信息, 员工工资表, 员工考勤分析, 月度费用预算及分析统计表, 电商客户反馈\u001b[0m\u001b[32;1m\u001b[1;3mThe most relevant tables for this question would be \"员工信息\" (employee information) and \"员工工资表\" (employee salary table). I should check their schemas to understand how to join them and find the marketing department employee with the highest base salary.\n", "\n", "Action: sql_db_schema\n", "Action Input: 员工信息, 员工工资表\u001b[0m\u001b[33;1m\u001b[1;3m\n", "CREATE TABLE \"员工信息\" (\n", "\t\"员工ID\" BIGINT, \n", "\t\"姓名\" TEXT, \n", "\t\"部门\" TEXT, \n", "\t\"职位\" TEXT, \n", "\t\"入职日期\" DATETIME, \n", "\t\"基本工资\" BIGINT, \n", "\t\"总薪酬\" BIGINT, \n", "\t\"绩效评分\" BIGINT, \n", "\t\"培训次数\" BIGINT, \n", "\t\"出勤率\" FLOAT, \n", "\t\"请假天数\" BIGINT, \n", "\t\"员工满意度\" BIGINT, \n", "\t\"健康状态\" TEXT, \n", "\t\"晋升次数\" BIGINT, \n", "\t\"离职状态\" TEXT\n", ")\n", "\n", "/*\n", "3 rows from 员工信息 table:\n", "员工ID\t姓名\t部门\t职位\t入职日期\t基本工资\t总薪酬\t绩效评分\t培训次数\t出勤率\t请假天数\t员工满意度\t健康状态\t晋升次数\t离职状态\n", "201\t李小华\t财务部\t财务主管\t2015-03-01 00:00:00\t9000\t13000\t92\t6\t0.98\t8\t85\t健康\t1\t未离职\n", "202\t王丽\t市场部\t市场专员\t2017-05-23 00:00:00\t7500\t11000\t85\t5\t0.97\t5\t90\t健康\t0\t未离职\n", "203\t张强\tIT部\t高级开发\t2016-08-15 00:00:00\t15000\t23000\t88\t7\t0.95\t10\t82\t健康\t2\t未离职\n", "*/\n", "\n", "\n", "CREATE TABLE \"员工工资表\" (\n", "\t\"姓名\" TEXT, \n", "\t\"部门\" TEXT, \n", "\t\"基本工资\" BIGINT, \n", "\t\"补贴补助\" BIGINT, \n", "\t\"奖金\" BIGINT, \n", "\t\"应发工资\" BIGINT, \n", "\t\"其他扣款\" BIGINT, \n", "\t\"社会保险\" BIGINT, \n", "\t\"补发工资\" BIGINT, \n", "\t\"实发工资\" BIGINT\n", ")\n", "\n", "/*\n", "3 rows from 员工工资表 table:\n", "姓名\t部门\t基本工资\t补贴补助\t奖金\t应发工资\t其他扣款\t社会保险\t补发工资\t实发工资\n", "曹玉凤\t行政部\t3350\t100\t100\t3550\t0\t420\t2000\t5130\n", "陈虹\t行政部\t3350\t100\t100\t3550\t0\t420\t2000\t5130\n", "严嘉\t财务部\t4500\t200\t20\t4720\t0\t420\t2000\t6300\n", "*/\u001b[0m\u001b[32;1m\u001b[1;3mI can see that both tables contain information about employees' departments and base salaries. The \"员工信息\" table has a \"部门\" column and \"基本工资\" column, and the \"员工工资表\" also has these columns. Since the question specifically asks about the marketing department, I'll need to filter for that department.\n", "\n", "Looking at the sample data, the marketing department is called \"市场部\" in Chinese. I should query the \"员工信息\" table to find the employee with the highest base salary in the marketing department.\n", "\n", "Action: sql_db_query_checker\n", "Action Input: SELECT 姓名, 基本工资 FROM 员工信息 WHERE 部门 = '市场部' ORDER BY 基本工资 DESC LIMIT 1\u001b[0m\u001b[36;1m\u001b[1;3mThe original SQL query appears to be correct. Here are the reasons why none of the mentioned common mistakes apply:\n", "\n", "1. There are no NOT IN operations\n", "2. There are no UNION operations\n", "3. There are no BETWEEN operations\n", "4. No data type mismatches in predicates (comparing string to string)\n", "5. Identifiers are properly quoted (assuming 姓名, 基本工资, 部门 are valid column names)\n", "6. No functions are being used that require arguments\n", "7. No casting needed\n", "8. No joins are being used\n", "\n", "Here is the original query:\n", "\n", "```sql\n", "SELECT 姓名, 基本工资 FROM 员工信息 WHERE 部门 = '市场部' ORDER BY 基本工资 DESC LIMIT 1\n", "```\u001b[0m\u001b[32;1m\u001b[1;3mThought: The query checker confirmed my query is correct. Now I'll execute it to get the answer.\n", "\n", "Action: sql_db_query\n", "Action Input: SELECT 姓名, 基本工资 FROM 员工信息 WHERE 部门 = '市场部' ORDER BY 基本工资 DESC LIMIT 1\u001b[0m\u001b[36;1m\u001b[1;3m[('萧峰', 10000)]\u001b[0m\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: 在营销部门（市场部），基本工资最高的是萧峰，基本工资为10000元。\n", "\n", "Explanation:\n", "我查询了\"员工信息\"表，筛选出部门为\"市场部\"的员工，并按基本工资降序排列，限制结果为1条记录。查询结果显示市场部基本工资最高的员工是萧峰，基本工资为10000元。\n", "\n", "使用的SQL查询如下：\n", "```sql\n", "SELECT 姓名, 基本工资 FROM 员工信息 WHERE 部门 = '市场部' ORDER BY 基本工资 DESC LIMIT 1\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "---------------------------------\n"]}, {"data": {"text/markdown": ["在营销部门（市场部），基本工资最高的是萧峰，基本工资为10000元。\n", "\n", "Explanation:\n", "我查询了\"员工信息\"表，筛选出部门为\"市场部\"的员工，并按基本工资降序排列，限制结果为1条记录。查询结果显示市场部基本工资最高的员工是萧峰，基本工资为10000元。\n", "\n", "使用的SQL查询如下：\n", "```sql\n", "SELECT 姓名, 基本工资 FROM 员工信息 WHERE 部门 = '市场部' ORDER BY 基本工资 DESC LIMIT 1\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Markdown, display\n", "QUESTION = \"\"\"在营销部门，谁的基本工资最高？是多少？\n", "\"\"\"\n", "response=agent_executor_SQL.invoke(QUESTION)\n", "print('---------------------------------')\n", "display(Markdown(response['output']))"]}, {"cell_type": "code", "execution_count": 14, "id": "dd103b20", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T22:38:56.660919Z", "start_time": "2025-04-14T22:38:13.613919Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new SQL Agent Executor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction: sql_db_list_tables\n", "Action Input: \u001b[0m\u001b[38;5;200m\u001b[1;3m员工信息, 员工工资表, 员工考勤分析, 月度费用预算及分析统计表\u001b[0m\u001b[32;1m\u001b[1;3mThe most relevant table for this question appears to be \"员工考勤分析\" (employee attendance analysis). I should examine its schema to understand what columns are available.\n", "Action: sql_db_schema\n", "Action Input: 员工考勤分析\u001b[0m\u001b[33;1m\u001b[1;3m\n", "CREATE TABLE \"员工考勤分析\" (\n", "\t\"员工ID\" BIGINT, \n", "\t\"姓名\" TEXT, \n", "\t\"公司\" TEXT, \n", "\t\"部门\" TEXT, \n", "\t\"应到天数\" BIGINT, \n", "\t\"请假天数\" BIGINT, \n", "\t\"迟到次数\" BIGINT, \n", "\t\"早退\" FLOAT, \n", "\t\"旷工\" FLOAT, \n", "\t\"加班\" FLOAT\n", ")\n", "\n", "/*\n", "3 rows from 员工考勤分析 table:\n", "员工ID\t姓名\t公司\t部门\t应到天数\t请假天数\t迟到次数\t早退\t旷工\t加班\n", "1\t曹玉凤\t北京分公司\t行政部\t23\t0\t5\t0.0\t0.0\t1.0\n", "2\t陈虹\t深圳分公司\t财务部\t23\t0\t0\t0.0\t0.0\t0.0\n", "3\t严嘉\t北京分公司\t服务部\t23\t0\t0\t1.0\t1.0\t0.0\n", "*/\u001b[0m\u001b[32;1m\u001b[1;3mNow that I know the \"员工考勤分析\" table has a \"旷工\" (absenteeism) column, I can query for employees with 3 or more absenteeism occurrences.\n", "Action: sql_db_query_checker\n", "Action Input: SELECT \"员工ID\", \"姓名\", \"公司\", \"部门\", \"旷工\" FROM \"员工考勤分析\" WHERE \"旷工\" >= 3 ORDER BY \"旷工\" DESC LIMIT 30\u001b[0m\u001b[36;1m\u001b[1;3mThe original query does not contain any of the common mistakes listed. Here is the reproduced original query:\n", "\n", "```sql\n", "SELECT \"员工ID\", \"姓名\", \"公司\", \"部门\", \"旷工\" FROM \"员工考勤分析\" WHERE \"旷工\" >= 3 ORDER BY \"旷工\" DESC LIMIT 30\n", "```\u001b[0m\u001b[32;1m\u001b[1;3mThe query appears to be correct, so I'll execute it to find employees with 3 or more absenteeism occurrences.\n", "Action: sql_db_query\n", "Action Input: SELECT \"员工ID\", \"姓名\", \"公司\", \"部门\", \"旷工\" FROM \"员工考勤分析\" WHERE \"旷工\" >= 3 ORDER BY \"旷工\" DESC LIMIT 30\u001b[0m\u001b[36;1m\u001b[1;3m[(10, '秦灿灿', '深圳分公司', '研发部', 3.0), (27, '郎园', '北京分公司', '服务部', 3.0), (44, '钱迎春', '北京分公司', '研发部', 3.0), (61, '郑心媛', '深圳分公司', '服务部', 3.0), (78, '施丽', '深圳分公司', '销售部', 3.0)]\u001b[0m\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: 以下员工旷工达到3次或以上：\n", "- 秦灿灿 (深圳分公司, 研发部)\n", "- 郎园 (北京分公司, 服务部)\n", "- 钱迎春 (北京分公司, 研发部)\n", "- 郑心媛 (深圳分公司, 服务部)\n", "- 施丽 (深圳分公司, 销售部)\n", "\n", "Explanation:\n", "I queried the \"员工考勤分析\" table for employees with 3 or more absenteeism occurrences (\"旷工\" >= 3). The query returned 5 employees who meet this criteria. I selected their ID, name, company, department, and absenteeism count, ordered by the highest absenteeism first, and limited to 30 results (though only 5 were found).\n", "\n", "Here's the SQL query I used:\n", "```sql\n", "SELECT \"员工ID\", \"姓名\", \"公司\", \"部门\", \"旷工\" \n", "FROM \"员工考勤分析\" \n", "WHERE \"旷工\" >= 3 \n", "ORDER BY \"旷工\" DESC \n", "LIMIT 30\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "---------------------------------\n"]}, {"data": {"text/markdown": ["以下员工旷工达到3次或以上：\n", "- 秦灿灿 (深圳分公司, 研发部)\n", "- 郎园 (北京分公司, 服务部)\n", "- 钱迎春 (北京分公司, 研发部)\n", "- 郑心媛 (深圳分公司, 服务部)\n", "- 施丽 (深圳分公司, 销售部)\n", "\n", "Explanation:\n", "I queried the \"员工考勤分析\" table for employees with 3 or more absenteeism occurrences (\"旷工\" >= 3). The query returned 5 employees who meet this criteria. I selected their ID, name, company, department, and absenteeism count, ordered by the highest absenteeism first, and limited to 30 results (though only 5 were found).\n", "\n", "Here's the SQL query I used:\n", "```sql\n", "SELECT \"员工ID\", \"姓名\", \"公司\", \"部门\", \"旷工\" \n", "FROM \"员工考勤分析\" \n", "WHERE \"旷工\" >= 3 \n", "ORDER BY \"旷工\" DESC \n", "LIMIT 30\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["QUESTION = \"\"\"从员工考勤中分析，旷工达到3次，或3次以上的员工的哪些人？\n", "\"\"\"\n", "response=agent_executor_SQL.invoke(QUESTION)\n", "print('---------------------------------')\n", "display(Markdown(response['output']))"]}, {"cell_type": "code", "execution_count": 15, "id": "efb32448", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T22:41:06.157806Z", "start_time": "2025-04-14T22:40:22.548628Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new SQL Agent Executor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction: sql_db_list_tables\n", "Action Input: \u001b[0m\u001b[38;5;200m\u001b[1;3m员工信息, 员工工资表, 员工考勤分析, 月度费用预算及分析统计表\u001b[0m\u001b[32;1m\u001b[1;3mI need to find the relevant tables that contain department information and salary data. The \"员工工资表\" (employee salary table) and \"员工信息\" (employee information table) seem most relevant.\n", "Action: sql_db_schema\n", "Action Input: 员工工资表, 员工信息\u001b[0m\u001b[33;1m\u001b[1;3m\n", "CREATE TABLE \"员工信息\" (\n", "\t\"员工ID\" BIGINT, \n", "\t\"姓名\" TEXT, \n", "\t\"部门\" TEXT, \n", "\t\"职位\" TEXT, \n", "\t\"入职日期\" DATETIME, \n", "\t\"基本工资\" BIGINT, \n", "\t\"总薪酬\" BIGINT, \n", "\t\"绩效评分\" BIGINT, \n", "\t\"培训次数\" BIGINT, \n", "\t\"出勤率\" FLOAT, \n", "\t\"请假天数\" BIGINT, \n", "\t\"员工满意度\" BIGINT, \n", "\t\"健康状态\" TEXT, \n", "\t\"晋升次数\" BIGINT, \n", "\t\"离职状态\" TEXT\n", ")\n", "\n", "/*\n", "3 rows from 员工信息 table:\n", "员工ID\t姓名\t部门\t职位\t入职日期\t基本工资\t总薪酬\t绩效评分\t培训次数\t出勤率\t请假天数\t员工满意度\t健康状态\t晋升次数\t离职状态\n", "201\t李小华\t财务部\t财务主管\t2015-03-01 00:00:00\t9000\t13000\t92\t6\t0.98\t8\t85\t健康\t1\t未离职\n", "202\t王丽\t市场部\t市场专员\t2017-05-23 00:00:00\t7500\t11000\t85\t5\t0.97\t5\t90\t健康\t0\t未离职\n", "203\t张强\tIT部\t高级开发\t2016-08-15 00:00:00\t15000\t23000\t88\t7\t0.95\t10\t82\t健康\t2\t未离职\n", "*/\n", "\n", "\n", "CREATE TABLE \"员工工资表\" (\n", "\t\"姓名\" TEXT, \n", "\t\"部门\" TEXT, \n", "\t\"基本工资\" BIGINT, \n", "\t\"补贴补助\" BIGINT, \n", "\t\"奖金\" BIGINT, \n", "\t\"应发工资\" BIGINT, \n", "\t\"其他扣款\" BIGINT, \n", "\t\"社会保险\" BIGINT, \n", "\t\"补发工资\" BIGINT, \n", "\t\"实发工资\" BIGINT\n", ")\n", "\n", "/*\n", "3 rows from 员工工资表 table:\n", "姓名\t部门\t基本工资\t补贴补助\t奖金\t应发工资\t其他扣款\t社会保险\t补发工资\t实发工资\n", "曹玉凤\t行政部\t3350\t100\t100\t3550\t0\t420\t2000\t5130\n", "陈虹\t行政部\t3350\t100\t100\t3550\t0\t420\t2000\t5130\n", "严嘉\t财务部\t4500\t200\t20\t4720\t0\t420\t2000\t6300\n", "*/\u001b[0m\u001b[32;1m\u001b[1;3mI can see that both tables contain department information and salary data. The \"员工工资表\" has the \"实发工资\" (actual salary) field which is what we need. I can group by department and sum the actual salaries.\n", "\n", "First, I'll check if my query is correct before executing it.\n", "Action: sql_db_query_checker\n", "Action Input: SELECT 部门, SUM(实发工资) AS 部门实发工资总计 FROM 员工工资表 GROUP BY 部门 ORDER BY 部门实发工资总计 DESC LIMIT 30\u001b[0m\u001b[36;1m\u001b[1;3mThe original SQL query you provided does not contain any of the common mistakes listed. Here is the original query reproduced:\n", "\n", "```sql\n", "SELECT 部门, SUM(实发工资) AS 部门实发工资总计 FROM 员工工资表 GROUP BY 部门 ORDER BY 部门实发工资总计 DESC LIMIT 30\n", "```\u001b[0m\u001b[32;1m\u001b[1;3mThe query looks correct. Now I'll execute it to get the results.\n", "Action: sql_db_query\n", "Action Input: SELECT 部门, SUM(实发工资) AS 部门实发工资总计 FROM 员工工资表 GROUP BY 部门 ORDER BY 部门实发工资总计 DESC LIMIT 30\u001b[0m\u001b[36;1m\u001b[1;3m[('销售部', 50860), ('营销部', 43940), ('人力资源部', 35510), ('生产部', 27960), ('技术部', 27780), ('财务部', 25140), ('行政部', 20947), ('后勤部', 16760)]\u001b[0m\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: 不同部门的实发工资总计如下（按从高到低排序）：\n", "- 销售部: 50,860\n", "- 营销部: 43,940\n", "- 人力资源部: 35,510\n", "- 生产部: 27,960\n", "- 技术部: 27,780\n", "- 财务部: 25,140\n", "- 行政部: 20,947\n", "- 后勤部: 16,760\n", "\n", "Explanation:\n", "我查询了\"员工工资表\"中的数据，按部门分组并计算每个部门的实发工资总和。结果按实发工资总计从高到低排序，最多显示30条记录（实际返回8个部门的数据）。使用的SQL查询如下：\n", "\n", "```sql\n", "SELECT 部门, SUM(实发工资) AS 部门实发工资总计 \n", "FROM 员工工资表 \n", "GROUP BY 部门 \n", "ORDER BY 部门实发工资总计 DESC \n", "LIMIT 30\n", "```\n", "\n", "结果显示销售部的实发工资总计最高，达到50,860，其次是营销部43,940，后勤部的实发工资总计最低，为16,760。\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "---------------------------------\n"]}, {"data": {"text/markdown": ["不同部门的实发工资总计如下（按从高到低排序）：\n", "- 销售部: 50,860\n", "- 营销部: 43,940\n", "- 人力资源部: 35,510\n", "- 生产部: 27,960\n", "- 技术部: 27,780\n", "- 财务部: 25,140\n", "- 行政部: 20,947\n", "- 后勤部: 16,760\n", "\n", "Explanation:\n", "我查询了\"员工工资表\"中的数据，按部门分组并计算每个部门的实发工资总和。结果按实发工资总计从高到低排序，最多显示30条记录（实际返回8个部门的数据）。使用的SQL查询如下：\n", "\n", "```sql\n", "SELECT 部门, SUM(实发工资) AS 部门实发工资总计 \n", "FROM 员工工资表 \n", "GROUP BY 部门 \n", "ORDER BY 部门实发工资总计 DESC \n", "LIMIT 30\n", "```\n", "\n", "结果显示销售部的实发工资总计最高，达到50,860，其次是营销部43,940，后勤部的实发工资总计最低，为16,760。"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["QUESTION = \"\"\"从员工工资中分析，不同部门的实发工资是多少？\n", "\"\"\"\n", "response=agent_executor_SQL.invoke(QUESTION)\n", "print('---------------------------------')\n", "display(Markdown(response['output']))"]}, {"cell_type": "code", "execution_count": 20, "id": "e5b1bed3", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T22:46:39.473662Z", "start_time": "2025-04-14T22:45:55.431614Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new SQL Agent Executor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction: sql_db_list_tables\n", "Action Input: \u001b[0m\u001b[38;5;200m\u001b[1;3m员工信息, 员工工资表, 员工考勤分析, 月度费用预算及分析统计表, 电商客户反馈\u001b[0m\u001b[32;1m\u001b[1;3mThe most relevant table for this question appears to be \"电商客户反馈\" (e-commerce customer feedback). I need to check its schema to understand what columns are available.\n", "Action: sql_db_schema\n", "Action Input: 电商客户反馈\u001b[0m\u001b[33;1m\u001b[1;3m\n", "CREATE TABLE \"电商客户反馈\" (\n", "\t\"客户ID\" TEXT, \n", "\t\"反馈时间\" BIGINT, \n", "\t\"反馈类型\" TEXT, \n", "\t\"反馈内容\" TEXT, \n", "\t\"客户满意度\" BIGINT, \n", "\t\"问题严重性\" TEXT, \n", "\t\"处理状态\" TEXT, \n", "\t\"责任部门\" TEXT\n", ")\n", "\n", "/*\n", "3 rows from 电商客户反馈 table:\n", "客户ID\t反馈时间\t反馈类型\t反馈内容\t客户满意度\t问题严重性\t处理状态\t责任部门\n", "C001\t45474\t产品问题\t产品包装破损\t3\t严重\t待处理\t物流部\n", "C002\t45475\t服务问题\t客服响应慢\t2\t中等\t处理中\t人力资源部\n", "C003\t45476\t建议\t增加更多产品选项\t4\t轻微\t已解决\t研发部\n", "*/\u001b[0m\u001b[32;1m\u001b[1;3mNow I understand the structure of the \"电商客户反馈\" table. To answer the question, I need to:\n", "1. Filter for feedback where \"问题严重性\" = '严重' and \"处理状态\" = '待处理'\n", "2. Group by \"责任部门\" to get counts by department\n", "3. Count the number of such feedbacks\n", "\n", "I'll first check if my query is correct before executing it.\n", "Action: sql_db_query_checker\n", "Action Input: SELECT \"责任部门\", COUNT(*) as \"未处理严重反馈数量\" FROM \"电商客户反馈\" WHERE \"问题严重性\" = '严重' AND \"处理状态\" = '待处理' GROUP BY \"责任部门\" ORDER BY \"未处理严重反馈数量\" DESC LIMIT 30\u001b[0m\u001b[36;1m\u001b[1;3mSELECT \"责任部门\", COUNT(*) as \"未处理严重反馈数量\" FROM \"电商客户反馈\" WHERE \"问题严重性\" = '严重' AND \"处理状态\" = '待处理' GROUP BY \"责任部门\" ORDER BY \"未处理严重反馈数量\" DESC LIMIT 30\u001b[0m\u001b[32;1m\u001b[1;3mThe query looks correct. Now I'll execute it to get the results.\n", "Action: sql_db_query\n", "Action Input: SELECT \"责任部门\", COUNT(*) as \"未处理严重反馈数量\" FROM \"电商客户反馈\" WHERE \"问题严重性\" = '严重' AND \"处理状态\" = '待处理' GROUP BY \"责任部门\" ORDER BY \"未处理严重反馈数量\" DESC LIMIT 30\u001b[0m\u001b[36;1m\u001b[1;3m[('质检部', 2), ('研发部', 2), ('物流部', 2)]\u001b[0m\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: 根据电商客户反馈数据，目前各部门未处理的严重反馈数量如下：\n", "- 质检部: 2条\n", "- 研发部: 2条\n", "- 物流部: 2条\n", "\n", "Explanation:\n", "我查询了\"电商客户反馈\"表，筛选出问题严重性为\"严重\"且处理状态为\"待处理\"的记录，然后按责任部门分组统计数量。查询结果显示了每个部门未处理的严重反馈数量。使用的SQL查询如下：\n", "\n", "```sql\n", "SELECT \"责任部门\", COUNT(*) as \"未处理严重反馈数量\" \n", "FROM \"电商客户反馈\" \n", "WHERE \"问题严重性\" = '严重' AND \"处理状态\" = '待处理' \n", "GROUP BY \"责任部门\" \n", "ORDER BY \"未处理严重反馈数量\" DESC \n", "LIMIT 30\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "---------------------------------\n"]}, {"data": {"text/markdown": ["根据电商客户反馈数据，目前各部门未处理的严重反馈数量如下：\n", "- 质检部: 2条\n", "- 研发部: 2条\n", "- 物流部: 2条\n", "\n", "Explanation:\n", "我查询了\"电商客户反馈\"表，筛选出问题严重性为\"严重\"且处理状态为\"待处理\"的记录，然后按责任部门分组统计数量。查询结果显示了每个部门未处理的严重反馈数量。使用的SQL查询如下：\n", "\n", "```sql\n", "SELECT \"责任部门\", COUNT(*) as \"未处理严重反馈数量\" \n", "FROM \"电商客户反馈\" \n", "WHERE \"问题严重性\" = '严重' AND \"处理状态\" = '待处理' \n", "GROUP BY \"责任部门\" \n", "ORDER BY \"未处理严重反馈数量\" DESC \n", "LIMIT 30\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["QUESTION = \"\"\"从电商客户反馈中分析，还有多少反馈是严重的，并且还没有处理的？请按照部门统计数量。\n", "\"\"\"\n", "response=agent_executor_SQL.invoke(QUESTION)\n", "print('---------------------------------')\n", "display(Markdown(response['output']))"]}, {"cell_type": "code", "execution_count": 21, "id": "8e1d82c8", "metadata": {"ExecuteTime": {"end_time": "2025-04-14T22:50:27.907160Z", "start_time": "2025-04-14T22:49:45.897343Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new SQL Agent Executor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction: sql_db_list_tables\n", "Action Input: \u001b[0m\u001b[38;5;200m\u001b[1;3m员工信息, 员工工资表, 员工考勤分析, 月度费用预算及分析统计表, 电商客户反馈\u001b[0m\u001b[32;1m\u001b[1;3mThe \"月度费用预算及分析统计表\" (Monthly Expense Budget and Analysis Statistics Table) seems most relevant for this question. I should examine its schema to understand what columns are available.\n", "Action: sql_db_schema\n", "Action Input: 月度费用预算及分析统计表\u001b[0m\u001b[33;1m\u001b[1;3m\n", "CREATE TABLE \"月度费用预算及分析统计表\" (\n", "\t\"费用类别\" TEXT, \n", "\t\"费用预算(年度)\" BIGINT, \n", "\t\"1月\" BIGINT, \n", "\t\"2月\" BIGINT, \n", "\t\"3月\" BIGINT, \n", "\t\"4月\" BIGINT, \n", "\t\"5月\" BIGINT, \n", "\t\"6月\" BIGINT, \n", "\t\"7月\" BIGINT, \n", "\t\"8月\" BIGINT, \n", "\t\"9月\" BIGINT, \n", "\t\"10月\" BIGINT, \n", "\t\"11月\" BIGINT, \n", "\t\"12月\" BIGINT\n", ")\n", "\n", "/*\n", "3 rows from 月度费用预算及分析统计表 table:\n", "费用类别\t费用预算(年度)\t1月\t2月\t3月\t4月\t5月\t6月\t7月\t8月\t9月\t10月\t11月\t12月\n", "人力费用\t300\t29\t115\t8\t315\t39\t38\t215\t34\t7\t38\t42\t115\n", "办公费用\t500\t17\t21\t38\t13\t20\t29\t17\t32\t37\t20\t34\t39\n", "物流费用\t480\t42\t29\t44\t38\t39\t38\t37\t11\t29\t215\t36\t24\n", "*/\u001b[0m\u001b[32;1m\u001b[1;3mI need to calculate the sum of all monthly expenses from January to December and compare it with the annual budget for logistics. I'll first query the total sum of all monthly expenses, then separately query the logistics budget and actual expenses.\n", "\n", "Action: sql_db_query\n", "Action Input: SELECT SUM(\"1月\"+\"2月\"+\"3月\"+\"4月\"+\"5月\"+\"6月\"+\"7月\"+\"8月\"+\"9月\"+\"10月\"+\"11月\"+\"12月\") AS total_expenses, SUM(\"费用预算(年度)\") AS total_budget FROM \"月度费用预算及分析统计表\"\n", "\u001b[0m\u001b[36;1m\u001b[1;3m[(4016, 4395)]\u001b[0m\u001b[32;1m\u001b[1;3mNow I need to specifically check the logistics budget and actual expenses to make the comparison.\n", "\n", "Action: sql_db_query\n", "Action Input: SELECT \"费用预算(年度)\" AS logistics_budget, (\"1月\"+\"2月\"+\"3月\"+\"4月\"+\"5月\"+\"6月\"+\"7月\"+\"8月\"+\"9月\"+\"10月\"+\"11月\"+\"12月\") AS logistics_actual FROM \"月度费用预算及分析统计表\" WHERE \"费用类别\" = '物流费用'\n", "\u001b[0m\u001b[36;1m\u001b[1;3m[(480, 582)]\u001b[0m\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: 从1月到12月的总费用合计是4016元，而总预算是4395元。具体到物流费用，预算为480元，实际支出为582元。因此，物流费用超过了预算（超出102元），但总体费用没有超过总预算。\n", "\n", "Explanation:\n", "1. 首先我查询了所有费用类别的1-12月总和以及年度总预算：\n", "```sql\n", "SELECT SUM(\"1月\"+\"2月\"+\"3月\"+\"4月\"+\"5月\"+\"6月\"+\"7月\"+\"8月\"+\"9月\"+\"10月\"+\"11月\"+\"12月\") AS total_expenses, SUM(\"费用预算(年度)\") AS total_budget FROM \"月度费用预算及分析统计表\"\n", "```\n", "结果：总支出4016元，总预算4395元（未超总预算）\n", "\n", "2. 然后我专门查询了物流费用的预算和实际支出：\n", "```sql\n", "SELECT \"费用预算(年度)\" AS logistics_budget, (\"1月\"+\"2月\"+\"3月\"+\"4月\"+\"5月\"+\"6月\"+\"7月\"+\"8月\"+\"9月\"+\"10月\"+\"11月\"+\"12月\") AS logistics_actual FROM \"月度费用预算及分析统计表\" WHERE \"费用类别\" = '物流费用'\n", "```\n", "结果：物流预算480元，实际支出582元（超支102元）\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "---------------------------------\n"]}, {"data": {"text/markdown": ["从1月到12月的总费用合计是4016元，而总预算是4395元。具体到物流费用，预算为480元，实际支出为582元。因此，物流费用超过了预算（超出102元），但总体费用没有超过总预算。\n", "\n", "Explanation:\n", "1. 首先我查询了所有费用类别的1-12月总和以及年度总预算：\n", "```sql\n", "SELECT SUM(\"1月\"+\"2月\"+\"3月\"+\"4月\"+\"5月\"+\"6月\"+\"7月\"+\"8月\"+\"9月\"+\"10月\"+\"11月\"+\"12月\") AS total_expenses, SUM(\"费用预算(年度)\") AS total_budget FROM \"月度费用预算及分析统计表\"\n", "```\n", "结果：总支出4016元，总预算4395元（未超总预算）\n", "\n", "2. 然后我专门查询了物流费用的预算和实际支出：\n", "```sql\n", "SELECT \"费用预算(年度)\" AS logistics_budget, (\"1月\"+\"2月\"+\"3月\"+\"4月\"+\"5月\"+\"6月\"+\"7月\"+\"8月\"+\"9月\"+\"10月\"+\"11月\"+\"12月\") AS logistics_actual FROM \"月度费用预算及分析统计表\" WHERE \"费用类别\" = '物流费用'\n", "```\n", "结果：物流预算480元，实际支出582元（超支102元）"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["QUESTION = \"\"\"从月度费用预算中分析，从1月到12月合计是多少？和物流的预算相比，是超过预算了，还是没有超预算？\n", "\"\"\"\n", "response=agent_executor_SQL.invoke(QUESTION)\n", "print('---------------------------------')\n", "display(Markdown(response['output']))"]}, {"cell_type": "code", "execution_count": null, "id": "792457d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}