import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os


class OpticalPathAnalyzer:
    def __init__(self):
        self.data = None
        self.start_date = ""
        self.end_date = ""
        self.processed_data = None

        # 地区映射字典（市区版）
        self.area_mapping = {
            '鼓楼': '鼓楼',
            '化工园': '化工园',
            '建邺': '建邺',
            '栖霞': '栖霞',
            '秦淮': '秦淮',
            '玄武': '玄武',
            '雨花': '雨花'
        }

        # 标准区域列表（市区版）
        self.standard_areas = ['鼓楼', '化工园', '建邺', '栖霞', '秦淮', '玄武', '雨花']

    def load_and_preprocess_data(self, file_path):
        """加载并预处理数据"""
        try:
            # 读取Excel文件
            self.data = pd.read_excel(file_path)

            # 标准化数据
            self.standardize_data()

            return True
        except Exception as e:
            try:
                messagebox.showerror("错误", f"文件读取失败: {str(e)}")
            except:
                print(f"文件读取失败: {str(e)}")
            return False

    def standardize_data(self):
        """标准化数据字段"""
        df = self.data.copy()

        # 标准化RFID改造状态
        # 0和85203972为未改造，1和85203974为已改造
        df['rfid_reformed'] = df['a端设备是否完成frid改造1'].apply(
            lambda x: '已改造' if str(x).replace('.0', '') in ['1', '85203974'] else '未改造'
        )

        # 标准化绑定状态
        df['binding_status'] = df.apply(self._determine_binding_status, axis=1)

        # 标准化合格状态
        df['label_qualified'] = df['纸质标签是否合格'].apply(
            lambda x: '合格' if str(x) in ['合格', '是', '正确'] else '不合格'
        )

        # 标准化RFID标签正确性
        df['rfid_correct'] = df['A端RFID标签是否正确'].apply(
            lambda x: '正确' if str(x) in ['正确', '是', '合格'] else '不正确'
        )

        # 标准化地区名称
        df['标准地区'] = df['地区'].map(self.area_mapping).fillna(df['地区'])

        self.processed_data = df

    def _determine_binding_status(self, row):
        """判断绑定状态 - 直接基于'A端是否绑定RFID标签'列"""
        rfid_binding = str(row['A端是否绑定RFID标签']).strip()

        if rfid_binding == '是':
            return '已绑定'
        else:  # 否 或其他值都视为未绑定
            return '未绑定'

    def calculate_statistics(self):
        """计算所有统计数据"""
        df = self.processed_data

        # 基础统计
        total_ports = len(df)

        # 纸质标签统计
        label_stats = self._calculate_label_statistics(df)

        # RFID统计
        rfid_stats = self._calculate_rfid_statistics(df)

        # 应绑未绑统计已包含在RFID统计中

        return {
            'total_ports': total_ports,
            'label_stats': label_stats,
            'rfid_stats': rfid_stats
        }

    def _calculate_label_statistics(self, df):
        """计算纸质标签统计"""
        # 按地区和设备类型统计纸质标签合格情况
        label_stats = df.groupby(['标准地区', 'a端设备资源规格', 'label_qualified']).size().unstack(fill_value=0)
        return label_stats

    def _calculate_rfid_statistics(self, df):
        """计算RFID统计"""
        # 按地区统计RFID相关数据
        rfid_stats = {}

        for area in self.standard_areas:
            area_data = df[df['标准地区'] == area]

            # 1. 已绑定RFID数量：'A端是否绑定RFID标签' = "是"
            bound_rfid = len(area_data[area_data['A端是否绑定RFID标签'] == '是'])

            # 2. 未绑定RFID数量：'A端是否绑定RFID标签' = "否"
            unbound_rfid = len(area_data[area_data['A端是否绑定RFID标签'] == '否'])

            # 3. 已绑RFID不合格数：已绑定且'A端RFID标签是否正确' = "否"
            bound_incorrect = len(area_data[
                (area_data['A端是否绑定RFID标签'] == '是') &
                (area_data['A端RFID标签是否正确'] == '否')
            ])

            # 4. 应绑未绑数量：未绑定 + 已改造 + 标记为"应绑未绑"
            should_bind = len(area_data[
                (area_data['A端是否绑定RFID标签'] == '否') &
                (area_data['a端设备是否完成frid改造1'].astype(str).str.replace('.0', '').isin(['1', '85203974'])) &
                (area_data['A端RFID标签是否正确'] == '应绑未绑')
            ])

            rfid_stats[area] = {
                'bound': bound_rfid,
                'bound_incorrect': bound_incorrect,
                'unbound': unbound_rfid,
                'should_bind': should_bind,
                'total': len(area_data)
            }

        return rfid_stats

    def _read_historical_data(self, output_path):
        """读取历史报告中的第一部分数据"""
        import os
        if not os.path.exists(output_path):
            return [], []  # 返回空的历史数据

        try:
            # 读取现有报告
            existing_df = pd.read_excel(output_path, sheet_name='汇总表', header=None)

            # 提取ODF整改单历史数据 (第5-15行) - 市区版扩展到K列
            odf_history = []
            for row in range(4, 16):  # 第5-16行 (0-based: 4-15)
                if row < len(existing_df):
                    row_data = existing_df.iloc[row, :11].tolist()  # A-K列（市区版多1列）
                    # 检查是否有有效数据（第一列不为空且不是"总计"）
                    if pd.notna(row_data[0]) and str(row_data[0]).strip() and str(row_data[0]).strip() != '总计':
                        odf_history.append(row_data)

            # 提取光交整改单历史数据 (第20-30行) - 市区版扩展到K列
            junction_history = []
            for row in range(19, 31):  # 第20-31行 (0-based: 19-30)
                if row < len(existing_df):
                    row_data = existing_df.iloc[row, :11].tolist()  # A-K列（市区版多1列）
                    # 检查是否有有效数据
                    if pd.notna(row_data[0]) and str(row_data[0]).strip() and str(row_data[0]).strip() != '总计':
                        junction_history.append(row_data)

            return odf_history, junction_history

        except Exception as e:
            print(f"读取历史数据失败: {e}")
            return [], []

    def generate_excel_report(self, output_path, check_period, log_func=None):
        """生成Excel报告"""
        def log(message):
            if log_func:
                log_func(message)
            else:
                print(message)

        # 读取历史数据
        log("📂 正在读取历史数据...")
        odf_history, junction_history = self._read_historical_data(output_path)
        if odf_history or junction_history:
            log(f"✅ 读取到历史数据: ODF {len(odf_history)}条, 光交 {len(junction_history)}条")
        else:
            log("📝 未找到历史数据，将创建新报告")

        # 计算统计数据
        log("📊 正在计算统计数据...")
        stats = self.calculate_statistics()

        # 创建工作簿
        log("📋 正在创建Excel工作簿...")
        wb = Workbook()
        ws1 = wb.active
        ws1.title = "汇总表"

        # 生成Sheet1内容
        log("📝 正在生成汇总表...")
        self._create_sheet1(ws1, stats, check_period, odf_history, junction_history)

        # 创建Sheet2
        log("📋 正在生成本期新增不合格清单表...")
        ws2 = wb.create_sheet("本期新增不合格清单")
        self._create_sheet2(ws2)

        # 保存文件
        log("💾 正在保存文件...")
        wb.save(output_path)
        log(f"✅ 报告已保存至: {output_path}")

        try:
            messagebox.showinfo("成功", f"报告已生成：{output_path}")
        except:
            print(f"报告已生成：{output_path}")

    def _create_sheet1(self, ws, stats, check_period, odf_history=None, junction_history=None):
        """创建Sheet1统计报告"""
        # 1. 总结部分 (A1:V1)
        self._create_summary_section(ws, stats, check_period)

        # 2. 第一部分表格 - ODF纸质标签整改单 (A4:J16) - 包含历史数据
        self._create_odf_label_table(ws, stats, check_period, odf_history or [])

        # 3. 第二部分表格 - 光交接箱纸质标签整改单 (A19:J31) - 包含历史数据
        self._create_junction_box_label_table(ws, stats, check_period, junction_history or [])

        # 4. 纸质标签统计 (L3:X12)
        self._create_label_statistics_table(ws, stats)

        # 5. 电子标签统计 (L19:R28)
        self._create_electronic_label_table(ws, stats)

        # 设置格式
        self._apply_formatting(ws)

    def _create_summary_section(self, ws, stats, check_period):
        """创建总结部分"""
        from openpyxl.styles import Font
        from openpyxl.styles.colors import Color

        total_ports = stats['total_ports']

        # 计算纸质标签准确率
        label_stats = stats['label_stats']
        qualified_count = label_stats.get('合格', pd.Series(0)).sum() if '合格' in label_stats.columns else 0
        unqualified_count = label_stats.get('不合格', pd.Series(0)).sum() if '不合格' in label_stats.columns else 0
        label_accuracy = (qualified_count / total_ports * 100) if total_ports > 0 else 0

        # 分别计算ODF和光交的不合格数量
        df = self.processed_data
        if df is not None:
            # ODF不合格数量
            odf_unqualified = len(df[
                ((df['a端设备资源规格'] == '光配线架') |
                 (df['a端设备资源规格'].str.contains('ODF', na=False))) &
                (df['label_qualified'] == '不合格')
            ])

            # 光交不合格数量
            junction_unqualified = len(df[
                (df['a端设备资源规格'].str.contains('光交', na=False)) &
                (df['label_qualified'] == '不合格')
            ])
        else:
            odf_unqualified = 0
            junction_unqualified = 0

        # RFID统计
        rfid_stats = stats['rfid_stats']
        total_bound = sum([area_stat['bound'] for area_stat in rfid_stats.values()])
        total_bound_incorrect = sum([area_stat['bound_incorrect'] for area_stat in rfid_stats.values()])
        total_should_bind = sum([area_stat['should_bind'] for area_stat in rfid_stats.values()])

        rfid_accuracy = ((total_bound - total_bound_incorrect) / total_bound * 100) if total_bound > 0 else 0

        # 去除年份，只保留月日
        period_parts = check_period.split('-')
        if len(period_parts) == 2:
            start_part = period_parts[0].split('.')
            end_part = period_parts[1].split('.')
            if len(start_part) >= 3 and len(end_part) >= 3:
                period_display = f"{start_part[1]}.{start_part[2]}-{end_part[1]}.{end_part[2]}"
            else:
                period_display = check_period
        else:
            period_display = check_period

        # 合并文本，在单元格内分行
        combined_text = (
            f"{period_display}南京市区现场检查IOM工单光路端子共{total_ports}个,"
            f"纸质标签准确率为{label_accuracy:.2f}%"
            f"（ODF{odf_unqualified}个端子、光交{junction_unqualified}个端子不合格）\n"
            f"其中绑定RFID标签端子共{total_bound}个，"
            f"已绑RFID标签端子准确数{total_bound - total_bound_incorrect}个，"
            f"RFID标签绑定准确率{rfid_accuracy:.2f}%；"
            f"应绑未绑端子共{total_should_bind}个"
        )

        # 写入合并单元格
        ws.merge_cells('A1:V1')

        # 暂时使用简单文本格式，避免RichText兼容性问题
        from openpyxl.styles import Alignment

        ws['A1'] = combined_text

        # 设置字体格式 - 宋体12号加粗
        ws['A1'].font = Font(name='宋体', size=12, bold=True)
        ws.row_dimensions[1].height = 40

        # 设置居中对齐和自动换行
        ws['A1'].alignment = Alignment(wrap_text=True, vertical='center', horizontal='center')

    def _create_odf_label_table(self, ws, stats, check_period, odf_history=None):
        """创建ODF纸质标签整改单表格"""
        from openpyxl.styles import Font, PatternFill
        from datetime import datetime, timedelta

        # 统一标题（只在第一次调用时添加）
        ws.merge_cells('A3:B3')
        ws['A3'] = '一、纸质标签整改单：'

        # 表头（上移到第4行）- 市区版7个地区
        headers = ['日期/光配线架', '鼓楼', '化工园', '建邺', '栖霞', '秦淮', '玄武', '雨花', '总计', '整治单月份', '整治截止时间']
        for col, header in enumerate(headers, 1):
            ws.cell(4, col, header)

        # 数据填充 - 先填充历史数据，再添加当前周期数据
        current_row = 5  # 从第5行开始（上移1行）

        # 1. 填充历史数据
        if odf_history:
            for history_row in odf_history:
                for col, value in enumerate(history_row, 1):  # A-K列（市区版多1列）
                    if pd.notna(value):
                        ws.cell(current_row, col, value)
                current_row += 1

        # 2. 填充当前周期数据
        df = self.processed_data
        if df is not None:
            # 筛选光配线架的不合格端子数据
            odf_unqualified = df[
                ((df['a端设备资源规格'] == '光配线架') |
                 (df['a端设备资源规格'].str.contains('ODF', na=False))) &
                (df['label_qualified'] == '不合格')
            ]

            # 按地区统计不合格数量
            area_counts = {}
            has_data = False
            for area in self.standard_areas:
                area_data = odf_unqualified[odf_unqualified['标准地区'] == area]
                count = len(area_data)
                if count > 0:
                    area_counts[area] = count
                    has_data = True

            # 只有在有不合格数据时才填充当前周期行
            if has_data:
                # 处理检查周期日期
                period_display = self._format_period_for_table(check_period)
                ws.cell(current_row, 1, period_display)  # 日期/光配线架列

                # 填充各地区数据（只填充有数据的）
                for col, area in enumerate(self.standard_areas, 2):  # 从B列开始
                    if area in area_counts:
                        ws.cell(current_row, col, area_counts[area])

                # 总计列 - 使用动态公式（市区版到H列）
                ws.cell(current_row, 9, f"=SUM(B{current_row}:H{current_row})")

                # 整治单月份和截止时间（市区版向右移1列）
                month, deadline = self._calculate_deadline(check_period)
                ws.cell(current_row, 10, month)
                ws.cell(current_row, 11, deadline)

        # 添加总计行（A16:I16）- 市区版扩展到I列
        total_row = 16
        ws.cell(total_row, 1, "总计")
        for col in range(2, 10):  # B到I列（市区版多1列）
            ws.cell(total_row, col, f"=SUM({chr(64+col)}5:{chr(64+col)}15)")  # 修改求和范围

        # 应用表格样式 - 蓝色表样式中等深浅2（市区版扩展到K列）
        self._apply_table_style(ws, 'A4:K16', 'blue_medium')  # 修改样式范围

    def _format_period_for_table(self, check_period):
        """格式化检查周期用于表格显示"""
        # 去除年份，只保留月日
        period_parts = check_period.split('-')
        if len(period_parts) == 2:
            start_part = period_parts[0].split('.')
            end_part = period_parts[1].split('.')
            if len(start_part) >= 3 and len(end_part) >= 3:
                return f"{start_part[1]}.{start_part[2]}-{end_part[1]}.{end_part[2]}"
        return check_period

    def _calculate_deadline(self, check_period):
        """计算整治单月份和截止时间"""
        from datetime import datetime, timedelta

        try:
            # 提取周期的结束日期
            period_parts = check_period.split('-')
            if len(period_parts) == 2:
                end_date_str = period_parts[1]
                # 假设格式为 YYYY.M.D
                date_parts = end_date_str.split('.')
                if len(date_parts) == 3:
                    year, month, day = int(date_parts[0]), int(date_parts[1]), int(date_parts[2])
                    end_date = datetime(year, month, day)

                    # 整治单月份就是当前月份
                    month_str = f"{month}月"

                    # 截止时间是周期结束后7天
                    deadline_date = end_date + timedelta(days=7)
                    deadline_str = f"{deadline_date.month}.{deadline_date.day}"

                    return month_str, deadline_str
        except:
            pass

        return "当月", "月底前"

    def _apply_table_style(self, ws, range_str, style_type):
        """应用表格样式"""
        from openpyxl.styles import PatternFill, Font, Border, Side, Alignment

        # 解析范围
        start_cell, end_cell = range_str.split(':')
        start_col = ord(start_cell[0]) - ord('A') + 1
        start_row = int(start_cell[1:])
        end_col = ord(end_cell[0]) - ord('A') + 1
        end_row = int(end_cell[1:])

        # 蓝色表样式中等深浅2的颜色
        if style_type == 'blue_medium':
            header_fill = PatternFill(start_color='4F81BD', end_color='4F81BD', fill_type='solid')
            data_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')

        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 应用样式
        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                cell = ws.cell(row, col)
                cell.border = thin_border
                cell.alignment = Alignment(horizontal='center', vertical='center')

                # 表头行使用深色背景
                if row == start_row:
                    cell.fill = header_fill
                    cell.font = Font(bold=True, color='FFFFFF')
                else:
                    cell.fill = data_fill

    def _create_junction_box_label_table(self, ws, stats, check_period, junction_history=None):
        """创建光交接箱纸质标签整改单表格"""
        # 表头（上移到第19行，删除标题）- 市区版7个地区
        headers = ['日期/光交', '鼓楼', '化工园', '建邺', '栖霞', '秦淮', '玄武', '雨花', '总计', '整治单月份', '整治截止时间']
        for col, header in enumerate(headers, 1):
            ws.cell(19, col, header)

        # 数据填充 - 先填充历史数据，再添加当前周期数据
        current_row = 20  # 从第20行开始（上移1行）

        # 1. 填充历史数据
        if junction_history:
            for history_row in junction_history:
                for col, value in enumerate(history_row, 1):  # A-K列（市区版多1列）
                    if pd.notna(value):
                        ws.cell(current_row, col, value)
                current_row += 1

        # 2. 填充当前周期数据
        df = self.processed_data
        if df is not None:
            # 筛选光交接箱的不合格端子数据
            junction_unqualified = df[
                (df['a端设备资源规格'].str.contains('光交', na=False)) &
                (df['label_qualified'] == '不合格')
            ]

            # 按地区统计不合格数量
            area_counts = {}
            has_data = False
            for area in self.standard_areas:
                area_data = junction_unqualified[junction_unqualified['标准地区'] == area]
                count = len(area_data)
                if count > 0:
                    area_counts[area] = count
                    has_data = True

            # 只有在有不合格数据时才填充当前周期行
            if has_data:
                # 处理检查周期日期
                period_display = self._format_period_for_table(check_period)
                ws.cell(current_row, 1, period_display)  # 日期/光交列

                # 填充各地区数据（只填充有数据的）
                for col, area in enumerate(self.standard_areas, 2):  # 从B列开始
                    if area in area_counts:
                        ws.cell(current_row, col, area_counts[area])

                # 总计列 - 使用动态公式（市区版到H列）
                ws.cell(current_row, 9, f"=SUM(B{current_row}:H{current_row})")

                # 整治单月份和截止时间（市区版向右移1列）
                month, deadline = self._calculate_deadline(check_period)
                ws.cell(current_row, 10, month)
                ws.cell(current_row, 11, deadline)

        # 添加总计行（A31:I31）- 市区版扩展到I列
        total_row = 31
        ws.cell(total_row, 1, "总计")
        for col in range(2, 10):  # B到I列（市区版多1列）
            ws.cell(total_row, col, f"=SUM({chr(64+col)}20:{chr(64+col)}30)")  # 修改求和范围

        # 应用表格样式 - 蓝色表样式中等深浅2（市区版扩展到K列）
        self._apply_table_style(ws, 'A19:K31', 'blue_medium')  # 修改样式范围

    def _create_label_statistics_table(self, ws, stats):
        """创建纸质标签统计表格"""
        from openpyxl.styles import Border, Side, Alignment, Font

        # 表格标题（市区版右移1列）
        ws['M3'] = '二、纸质标签统计：'
        ws['M3'].font = Font(bold=True)

        # 表头设置（市区版右移1列）
        ws.merge_cells('M4:M5')
        ws['M4'] = '区域'
        ws['M4'].font = Font(name='Arial')

        ws.merge_cells('N4:Q4')
        ws['N4'] = '光配线架'
        ws['N4'].font = Font(name='Arial')

        ws.merge_cells('R4:U4')
        ws['R4'] = '光交接箱'
        ws['R4'].font = Font(name='Arial')

        ws.merge_cells('V4:Y4')
        ws['V4'] = '总计'
        ws['V4'].font = Font(name='Arial')

        # 子表头（市区版右移1列）
        sub_headers = ['不合格', '合格', '总数', '准确率']
        for i, header in enumerate(sub_headers):
            ws.cell(5, 14 + i, header).font = Font(name='Arial')  # N5:Q5
            ws.cell(5, 18 + i, header).font = Font(name='Arial')  # R5:U5
            ws.cell(5, 22 + i, header).font = Font(name='Arial')  # V5:Y5

        # 数据填充（市区版增加1行，右移1列）
        df = self.processed_data
        if df is not None:
            for row, area in enumerate(self.standard_areas + ['总计'], 6):
                ws.cell(row, 13, area)  # M6:M13 区域列（市区版7个地区+总计=8行）

                if area != '总计':
                    area_data = df[df['标准地区'] == area]

                    # 光配线架统计（包括'光配线架'和包含'ODF'的设备）
                    odf_data = area_data[
                        (area_data['a端设备资源规格'] == '光配线架') |
                        (area_data['a端设备资源规格'].str.contains('ODF', na=False))
                    ]
                    odf_unqualified = len(odf_data[odf_data['label_qualified'] == '不合格'])
                    odf_qualified = len(odf_data[odf_data['label_qualified'] == '合格'])
                    odf_total = len(odf_data)
                    odf_accuracy = (odf_qualified / odf_total * 100) if odf_total > 0 else 0

                    # 光交接箱统计
                    junction_data = area_data[area_data['a端设备资源规格'].str.contains('光交', na=False)]
                    junction_unqualified = len(junction_data[junction_data['label_qualified'] == '不合格'])
                    junction_qualified = len(junction_data[junction_data['label_qualified'] == '合格'])
                    junction_total = len(junction_data)
                    junction_accuracy = (junction_qualified / junction_total * 100) if junction_total > 0 else 0

                    # 填充光配线架数据 (N-Q列) - 市区版右移1列
                    if odf_total > 0:
                        if odf_unqualified > 0:
                            ws.cell(row, 14, odf_unqualified)  # N列：不合格
                        if odf_qualified > 0:
                            ws.cell(row, 15, odf_qualified)    # O列：合格
                        ws.cell(row, 16, odf_total)        # P列：总数
                        ws.cell(row, 17, f"{odf_accuracy:.2f}%")  # Q列：准确率
                        # 设置准确率字体为Arial
                        ws.cell(row, 17).font = Font(name='Arial')

                    # 填充光交接箱数据 (R-U列) - 市区版右移1列
                    if junction_total > 0:
                        if junction_unqualified > 0:
                            ws.cell(row, 18, junction_unqualified)  # R列：不合格
                        if junction_qualified > 0:
                            ws.cell(row, 19, junction_qualified)    # S列：合格
                        ws.cell(row, 20, junction_total)        # T列：总数
                        ws.cell(row, 21, f"{junction_accuracy:.2f}%")  # U列：准确率
                        # 设置准确率字体为Arial
                        ws.cell(row, 21).font = Font(name='Arial')

                    # 填充总计数据 (V-Y列) - 市区版右移1列
                    total_unqualified = odf_unqualified + junction_unqualified
                    total_qualified = odf_qualified + junction_qualified
                    total_all = odf_total + junction_total

                    if total_all > 0:
                        total_accuracy = (total_qualified / total_all * 100)
                        if total_unqualified > 0:
                            ws.cell(row, 22, total_unqualified)  # V列：不合格
                        if total_qualified > 0:
                            ws.cell(row, 23, total_qualified)    # W列：合格
                        ws.cell(row, 24, total_all)          # X列：总数
                        ws.cell(row, 25, f"{total_accuracy:.2f}%")  # Y列：准确率
                        # 设置准确率加粗
                        ws.cell(row, 25).font = Font(bold=True)
                else:
                    # 总计行使用公式 - 市区版右移1列，增加1行
                    for col in range(14, 25):  # N到X列（右移1列）
                        if col in [17, 21, 25]:  # 准确率列跳过（右移1列）
                            continue
                        # 检查该列是否有数据
                        has_data = False
                        for check_row in range(6, row):
                            if ws.cell(check_row, col).value:
                                has_data = True
                                break
                        if has_data:
                            ws.cell(row, col, f"=SUM({chr(64+col)}6:{chr(64+col)}{row-1})")

                    # 准确率公式 - 市区版右移1列
                    if ws.cell(row, 15).value and ws.cell(row, 16).value:  # ODF有数据（右移1列）
                        ws.cell(row, 17, f"=O{row}/P{row}")  # ODF准确率（右移1列）
                        ws.cell(row, 17).font = Font(name='Arial')
                        ws.cell(row, 17).number_format = '0.00%'

                    if ws.cell(row, 19).value and ws.cell(row, 20).value:  # 光交有数据（右移1列）
                        ws.cell(row, 21, f"=S{row}/T{row}")  # 光交准确率（右移1列）
                        ws.cell(row, 21).font = Font(name='Arial')
                        ws.cell(row, 21).number_format = '0.00%'

                    if ws.cell(row, 23).value and ws.cell(row, 24).value:  # 总计有数据（右移1列）
                        ws.cell(row, 25, f"=W{row}/X{row}")  # 总准确率（右移1列）
                        ws.cell(row, 25).font = Font(name='Arial')
                        ws.cell(row, 25).number_format = '0.00%'

        # 添加表格边框和居中对齐（市区版右移1列，增加1行）
        self._apply_table_borders_and_alignment(ws, 'M4:Y13')

    def _create_electronic_label_table(self, ws, stats):
        """创建电子标签统计表格"""
        from openpyxl.styles import Font

        # 表格标题（市区版右移1列）
        ws['M19'] = '三、电子标签统计：'
        ws['M19'].font = Font(bold=True)

        # 表头设置（市区版右移1列）
        headers = ['区域', '已绑定RFID', '已绑RFID不合格数', '未绑定RFID', '应绑未绑', '总检查端子数', '已绑RFID准确率']

        for col, header in enumerate(headers, 13):  # 从M列开始（右移1列）
            ws.merge_cells(f'{chr(64 + col)}20:{chr(64 + col)}21')
            cell = ws.cell(20, col, header)
            cell.font = Font(name='Arial')

        # 数据填充（市区版右移1列，增加1行）
        rfid_stats = stats['rfid_stats']
        for row, area in enumerate(self.standard_areas + ['总计'], 22):
            ws.cell(row, 13, area)  # M22:M29（市区版7个地区+总计=8行）

            if area in rfid_stats:
                area_stat = rfid_stats[area]
                # 填充数据，0值显示为空白（市区版右移1列）
                if area_stat['bound'] > 0:
                    ws.cell(row, 14, area_stat['bound'])  # 已绑定RFID（右移1列）
                if area_stat['bound_incorrect'] > 0:
                    ws.cell(row, 15, area_stat['bound_incorrect'])  # 已绑RFID不合格数（右移1列）
                if area_stat['unbound'] > 0:
                    ws.cell(row, 16, area_stat['unbound'])  # 未绑定RFID（右移1列）
                if area_stat['should_bind'] > 0:
                    ws.cell(row, 17, area_stat['should_bind'])  # 应绑未绑（右移1列）

                # 总检查端子数 = 已绑定 + 未绑定
                total_checked = area_stat['bound'] + area_stat['unbound']
                if total_checked > 0:
                    ws.cell(row, 18, total_checked)  # 直接填入数值（右移1列）

                # 已绑RFID准确率 = (已绑定 - 不合格) / 已绑定
                if area_stat['bound'] > 0:
                    accuracy = (area_stat['bound'] - area_stat['bound_incorrect']) / area_stat['bound']
                    # 只在准确率不为0时显示
                    if accuracy > 0:
                        ws.cell(row, 19, accuracy)  # 直接填入数值（右移1列）
                        ws.cell(row, 19).font = Font(name='Arial')
                        ws.cell(row, 19).number_format = '0.00%'
                # 如果没有已绑定的或准确率为0，不填充任何内容（保持空白）
            elif area == '总计':
                # 总计行使用求和公式（市区版右移1列）
                for col in range(14, 18):  # N到Q列（右移1列）
                    ws.cell(row, col, f"=SUM({chr(64+col)}22:{chr(64+col)}{row-1})")

                # 总检查端子数 = 总已绑定 + 总未绑定（右移1列）
                ws.cell(row, 18, f"=N{row}+P{row}")

                # 总准确率 = (总已绑定 - 总不合格) / 总已绑定，0值不显示（右移1列）
                ws.cell(row, 19, f"=IF(N{row}>0,(N{row}-O{row})/N{row},\"\")")
                ws.cell(row, 19).font = Font(name='Arial')
                ws.cell(row, 19).number_format = '0.00%'

        # 添加表格边框和居中对齐（市区版右移1列，增加1行）
        self._apply_table_borders_and_alignment(ws, 'M20:S29')

    def _apply_table_borders_and_alignment(self, ws, range_str):
        """为表格添加边框和居中对齐"""
        from openpyxl.styles import Border, Side, Alignment

        # 解析范围
        start_cell, end_cell = range_str.split(':')
        start_col = ord(start_cell[0]) - ord('A') + 1
        start_row = int(start_cell[1:])
        end_col = ord(end_cell[0]) - ord('A') + 1
        end_row = int(end_cell[1:])

        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 居中对齐
        center_alignment = Alignment(horizontal='center', vertical='center')

        # 应用样式
        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                cell = ws.cell(row, col)
                cell.border = thin_border
                cell.alignment = center_alignment

    def _create_sheet2(self, ws):
        """创建Sheet2异常清单"""
        if self.processed_data is None:
            return

        # 获取原始数据（不包含新增的处理列）
        original_df = self.data  # 使用原始数据而不是处理后的数据
        processed_df = self.processed_data

        # 纸质标签不合格清单
        unqualified_labels = processed_df[processed_df['label_qualified'] == '不合格']

        # 应绑未绑清单 - 处理系统导出的浮点数格式
        should_bind_list = processed_df[
            (processed_df['A端是否绑定RFID标签'] == '否') &
            (processed_df['a端设备是否完成frid改造1'].astype(str).str.replace('.0', '').isin(['1', '85203974'])) &
            (processed_df['A端RFID标签是否正确'] == '应绑未绑')
        ]

        # 写入纸质标签不合格清单
        ws['A1'] = '一、纸质标签不合格清单'

        # 写入表头（只使用原始列）
        if not unqualified_labels.empty:
            # 获取原始数据的列名作为表头
            original_headers = list(original_df.columns)
            for col, header in enumerate(original_headers, 1):
                ws.cell(2, col, header)

            # 写入不合格数据（只写入原始列的数据）
            unqualified_indices = unqualified_labels.index
            for row_idx, orig_idx in enumerate(unqualified_indices, 3):
                original_row = original_df.loc[orig_idx]
                for col_idx, value in enumerate(original_row, 1):
                    ws.cell(row_idx, col_idx, value)

        # 应绑未绑清单
        start_row = len(unqualified_labels) + 5 if not unqualified_labels.empty else 5
        ws.cell(start_row, 1, '二、应绑未绑清单')

        if not should_bind_list.empty:
            # 写入表头（只使用原始列）
            original_headers = list(original_df.columns)
            for col, header in enumerate(original_headers, 1):
                ws.cell(start_row + 1, col, header)

            # 写入应绑未绑数据（只写入原始列的数据）
            should_bind_indices = should_bind_list.index
            for row_idx, orig_idx in enumerate(should_bind_indices, start_row + 2):
                original_row = original_df.loc[orig_idx]
                for col_idx, value in enumerate(original_row, 1):
                    ws.cell(row_idx, col_idx, value)

    def _apply_formatting(self, ws):
        """应用格式设置"""
        # 设置字体加粗等格式
        bold_font = Font(bold=True)

        # 对表格标题和表头应用加粗格式
        title_cells = ['A3', 'L3', 'L19']
        for cell in title_cells:
            ws[cell].font = bold_font


# GUI界面类
class OpticalPathGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("光路检查结果自动化处理系统 - 市区版")
        self.root.geometry("600x400")

        self.analyzer = OpticalPathAnalyzer()
        self.file_path = ""

        self.create_widgets()

    def create_widgets(self):
        """创建GUI组件"""
        # 文件选择
        file_frame = ttk.Frame(self.root)
        file_frame.pack(pady=10)

        ttk.Label(file_frame, text="选择Excel文件:").pack(side=tk.LEFT)
        self.file_label = ttk.Label(file_frame, text="未选择文件", foreground="red")
        self.file_label.pack(side=tk.LEFT, padx=10)

        ttk.Button(file_frame, text="浏览", command=self.select_file).pack(side=tk.LEFT)

        # 日期输入
        date_frame = ttk.Frame(self.root)
        date_frame.pack(pady=10)

        ttk.Label(date_frame, text="检查周期:").pack(side=tk.LEFT)
        self.start_date = ttk.Entry(date_frame, width=12)
        self.start_date.pack(side=tk.LEFT, padx=5)
        self.start_date.insert(0, "2025.7.12")

        ttk.Label(date_frame, text="至").pack(side=tk.LEFT)
        self.end_date = ttk.Entry(date_frame, width=12)
        self.end_date.pack(side=tk.LEFT, padx=5)
        self.end_date.insert(0, "2025.7.18")

        # 处理按钮
        ttk.Button(self.root, text="生成报告", command=self.generate_report).pack(pady=20)

        # 状态显示
        status_frame = ttk.Frame(self.root)
        status_frame.pack(pady=10, fill=tk.BOTH, expand=True)

        ttk.Label(status_frame, text="操作日志:").pack(anchor=tk.W)

        # 创建带滚动条的文本框
        text_frame = ttk.Frame(status_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.status_text = tk.Text(text_frame, height=15, width=70, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化日志
        self.log_message("系统已启动，请选择Excel文件开始处理...")

    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.status_text.insert(tk.END, log_entry)
        self.status_text.see(tk.END)  # 自动滚动到最新消息
        self.root.update()  # 立即更新界面

    def select_file(self):
        """选择文件"""
        self.log_message("正在打开文件选择对话框...")

        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )

        if file_path:
            self.file_path = file_path
            filename = os.path.basename(file_path)
            self.file_label.config(text=filename, foreground="green")
            self.log_message(f"✅ 已选择文件: {filename}")
            self.log_message(f"文件路径: {file_path}")
        else:
            self.log_message("❌ 未选择文件")

    def generate_report(self):
        """生成报告"""
        self.log_message("🚀 开始生成报告...")

        if not self.file_path:
            self.log_message("❌ 错误：请先选择Excel文件")
            messagebox.showerror("错误", "请先选择Excel文件")
            return

        start_date = self.start_date.get()
        end_date = self.end_date.get()

        if not start_date or not end_date:
            self.log_message("❌ 错误：请输入检查周期")
            messagebox.showerror("错误", "请输入检查周期")
            return

        self.log_message(f"📅 检查周期: {start_date} 至 {end_date}")

        # 加载数据
        self.log_message("📂 正在加载和预处理数据...")
        if not self.analyzer.load_and_preprocess_data(self.file_path):
            self.log_message("❌ 数据加载失败")
            return

        self.log_message(f"✅ 数据加载成功，共 {len(self.analyzer.processed_data)} 条记录")

        # 计算统计数据
        self.log_message("📊 正在计算统计数据...")
        try:
            stats = self.analyzer.calculate_statistics()
            self.log_message(f"✅ 统计计算完成:")
            self.log_message(f"   - 总端子数: {stats['total_ports']}")

            # 显示各地区统计
            for area, area_stats in stats['rfid_stats'].items():
                self.log_message(f"   - {area}: 已绑定{area_stats['bound']}, 未绑定{area_stats['unbound']}, 应绑未绑{area_stats['should_bind']}")
        except Exception as e:
            self.log_message(f"❌ 统计计算失败: {str(e)}")
            return

        # 生成输出文件名
        output_filename = f"综维市区全量检查({start_date}-{end_date}).xlsx"
        self.log_message("💾 正在选择保存位置...")

        output_path = filedialog.asksaveasfilename(
            title="保存报告",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            initialfile=output_filename
        )

        if output_path:
            self.log_message(f"📁 保存路径: {output_path}")
            self.log_message("📝 正在生成Excel报告...")

            check_period = f"{start_date}-{end_date}"
            self.analyzer.generate_excel_report(output_path, check_period, self.log_message)

            self.log_message("🎉 报告生成完成！")
        else:
            self.log_message("❌ 未选择保存路径，操作已取消")


def main():
    """主函数"""
    app = OpticalPathGUI()
    app.root.mainloop()


if __name__ == "__main__":
    main()