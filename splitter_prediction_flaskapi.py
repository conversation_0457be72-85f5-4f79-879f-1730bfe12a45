"""
分光器端口预测API接口
基于 hybridmodel_predictor_modular.py 封装的RESTful API接口
用于前后端对接，提供预测分析服务

主要功能：
1. 单设备预测分析
2. 批量设备预测
3. 趋势数据分析
4. 风险评估计算
5. 业务洞察生成

接口设计：
- POST /api/predict/single - 单设备预测
- POST /api/predict/batch - 批量预测
- POST /api/analyze/trend - 趋势分析
- POST /api/analyze/risk - 风险评估
- GET /api/health - 健康检查

作者：基于原始预测模型封装
日期：2024-12-25
"""

import os
import pandas as pd
import numpy as np
import warnings
import random
import json
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

# 统计模型
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# 机器学习模型
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.linear_model import LinearRegression
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping, History
from tensorflow.keras.optimizers import Adam

# 抑制警告
warnings.filterwarnings('ignore')

# 检查TensorFlow是否可用
try:
    import tensorflow as tf
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

# 安全整数转换，避免NaN报错
def safe_int(val):
    return int(val) if pd.notna(val) else 0


class DeviceClassifier:
    """设备分类器模块 - 完全复制原始逻辑"""

    def classify_device_type(self, data, device_id):
        """基于数据量的两层分类策略"""
        data_points = len(data)

        # 第一层：数据量判断
        min_data_for_advanced = 8

        if data_points < min_data_for_advanced:
            return 'insufficient_data'

        # 第二层：数据充足的设备进行业务特征分类
        # 计算变异系数
        cv = data['实占率'].std() / data['实占率'].mean() if data['实占率'].mean() > 0 else 0

        # 扩容检测
        expansion_status = self._detect_expansion_advanced(data)

        # 业务特征分类
        if expansion_status in ['recent_expansion', 'historical_expansion']:
            return 'expanded'
        elif cv < 0.05:  # 稳定设备阈值
            return 'stable'
        elif cv < 0.15:  # 正常设备阈值
            return 'normal'
        else:  # 波动设备（包含原来的volatile和highly_volatile）
            return 'volatile'

    def _detect_expansion_advanced(self, data):
        """基于occupancy_rate_analysis.py的高级扩容检测"""
        if '容量变化' not in data.columns:
            return 'none'

        # 检查容量变化
        capacity_changes = data['容量变化'].sum()
        initial_capacity = data['分光器容量'].iloc[0] if len(data) > 0 else 1
        capacity_ratio = capacity_changes / initial_capacity if initial_capacity > 0 else 0

        # 检查实占率突降（扩容的典型特征）
        if '实占率变化' in data.columns:
            occupancy_changes = data['实占率变化']
            significant_drops = (occupancy_changes < -0.15).sum()  # 15%的突降
        else:
            significant_drops = 0

        # 检查近期扩容（最近12周）
        recent_weeks = min(12, len(data))
        if recent_weeks > 0:
            recent_data = data.iloc[-recent_weeks:]
            recent_capacity_change = recent_data['容量变化'].sum()
            recent_capacity_ratio = recent_capacity_change / initial_capacity if initial_capacity > 0 else 0

            if recent_capacity_ratio > 0.1:  # 近期容量增加超过10%
                return 'recent_expansion'

        # 检查历史扩容
        if capacity_ratio > 0.1 or significant_drops > 0:
            return 'historical_expansion'

        return 'none'


class TrendAnalyzer:
    """趋势分析器模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def analyze_device_trend(self, data, device_id, device_type='stable'):
        """通用设备趋势分析框架"""
        if len(data) < 3:
            return "数据不足，无法进行趋势分析"

        # 获取实占率序列
        occupancy_series = data['实占率'].values

        # 计算趋势指标
        recent_trend = self._calculate_recent_trend(occupancy_series)
        volatility = np.std(occupancy_series)
        current_level = occupancy_series[-1]

        # 根据设备类型生成分析
        if device_type == 'stable':
            return self._analyze_stable_device(recent_trend, volatility, current_level)
        elif device_type == 'expanded':
            return self._analyze_expanded_device(recent_trend, volatility, current_level, data)
        elif device_type == 'volatile':
            return self._analyze_volatile_device(recent_trend, volatility, current_level)
        else:
            return self._analyze_normal_device(recent_trend, volatility, current_level)

    def _calculate_recent_trend(self, series, window=4):
        """计算最近趋势"""
        if len(series) < window:
            window = len(series)

        recent_data = series[-window:]
        if len(recent_data) < 2:
            return 0

        # 使用线性回归计算趋势
        x = np.arange(len(recent_data))
        slope = np.polyfit(x, recent_data, 1)[0]
        return slope

    def _analyze_stable_device(self, trend, volatility, current_level):
        """分析稳定设备"""
        trend_desc = "稳定" if abs(trend) < 0.01 else ("上升" if trend > 0 else "下降")

        if current_level > 0.8:
            risk_level = "🔴 高风险"
        elif current_level > 0.6:
            risk_level = "🟡 中风险"
        else:
            risk_level = "🟢 低风险"

        return f"📏 业务长期稳定，无明显发展趋势 | ✅ 设备运行稳定 | {risk_level} 当前实占率{'较高，建议关注' if current_level > 0.6 else '正常'}"

    def _analyze_expanded_device(self, trend, volatility, current_level, data):
        """分析扩容设备"""
        # 检查扩容效果
        if '容量变化' in data.columns:
            recent_expansion = data['容量变化'].iloc[-4:].sum() > 0
            expansion_effect = "扩容有效" if current_level < 0.7 else "扩容效果有限"
        else:
            expansion_effect = "扩容状态未知"

        return f"🔧 {expansion_effect} | 📈 {'持续增长' if trend > 0.01 else '趋于稳定'} | 🎯 当前实占率{current_level:.1%}"

    def _analyze_volatile_device(self, trend, volatility, current_level):
        """分析波动设备"""
        volatility_desc = "高波动" if volatility > 0.15 else "中等波动"

        return f"⚡ {volatility_desc}设备 | 📊 {'上升趋势' if trend > 0.01 else ('下降趋势' if trend < -0.01 else '无明显趋势')} | ⚠️ 需密切监控"

    def _analyze_normal_device(self, trend, volatility, current_level):
        """分析正常设备"""
        trend_desc = "增长" if trend > 0.01 else ("下降" if trend < -0.01 else "稳定")

        return f"📈 业务{trend_desc}趋势 | 🎯 实占率{current_level:.1%} | ✅ 运行正常"


class ChangeDetector:
    """突变检测器模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def detect_sudden_change(self, data, window_size=2, threshold=0.15):
        """
        滑动窗口均值差检测突变点
        若有多个突变，优先选择最近的一次突变
        """
        if len(data) < window_size * 2:
            return None, 0

        # 从后往前检测，找到第一个（最近的）突变点
        for i in range(len(data) - window_size, window_size - 1, -1):
            before_mean = data[i-window_size:i].mean()
            after_mean = data[i:i+window_size].mean()
            diff = abs(after_mean - before_mean)

            if diff > threshold:
                return i, diff

        return None, 0

    def extract_trend_before_change(self, data):
        """提取突变前的趋势特征"""
        if len(data) < 3:
            return {'slope': 0, 'volatility': 0, 'direction': 'stable'}

        # 计算趋势斜率
        x = np.arange(len(data))
        slope = np.polyfit(x, data, 1)[0]

        # 计算波动性
        volatility = np.std(data)

        # 判断趋势方向
        if slope > 0.01:
            direction = 'increasing'
        elif slope < -0.01:
            direction = 'decreasing'
        else:
            direction = 'stable'

        return {
            'slope': slope,
            'volatility': volatility,
            'direction': direction
        }

    def predict_with_trend_transfer(self, before_data, after_data, test_length):
        """趋势迁移预测法"""
        if len(before_data) < 2:
            # 数据不足，使用突变后数据的均值
            if len(after_data) > 0:
                return [after_data.mean()] * test_length
            else:
                return [0.5] * test_length

        # 提取突变前趋势
        trend_features = self.extract_trend_before_change(before_data)

        # 获取新的基准水平（突变后的水平）
        if len(after_data) > 0:
            new_baseline = after_data.iloc[-1]  # 使用突变后的最新实占率作为基准
        else:
            new_baseline = before_data.iloc[-1]

        # 基于趋势斜率和新基准水平进行预测
        predictions = []
        for i in range(test_length):
            # 应用趋势到新基准水平
            trend_adjustment = trend_features['slope'] * (i + 1)
            prediction = new_baseline + trend_adjustment

            # 确保预测值在合理范围内
            prediction = max(0, min(1, prediction))
            predictions.append(prediction)

        return predictions


class PredictionEngine:
    """基础预测引擎模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def simple_average_predict(self, data):
        """简单平均预测（用于数据严重不足的设备 <5个数据点）"""
        if len(data) == 0:
            # 如果没有数据，使用默认值
            return {
                'occupancy': 0.5,
                'ports': 24,
                'demand': 0.3
            }

        # 计算平均值
        occupancy_avg = data['实占率'].mean()
        ports_avg = data['空闲端口数'].mean()
        demand_avg = data['潜在需求比'].mean() if '潜在需求比' in data.columns else 0.3

        return {
            'occupancy': occupancy_avg,
            'ports': max(0, safe_int(ports_avg)),
            'demand': demand_avg
        }

    def moving_average_predict(self, data, window=4):
        """移动平均预测（用于稳定设备）"""
        if len(data) < window:
            window = len(data)

        if window == 0:
            return self.simple_average_predict(data)

        # 计算移动平均
        recent_data = data.iloc[-window:]
        occupancy_ma = recent_data['实占率'].mean()
        ports_ma = recent_data['空闲端口数'].mean()
        demand_ma = recent_data['潜在需求比'].mean() if '潜在需求比' in recent_data.columns else 0.3

        return {
            'occupancy': occupancy_ma,
            'ports': max(0, safe_int(ports_ma)),
            'demand': demand_ma
        }

    def linear_regression_predict(self, data):
        """线性回归预测"""
        if len(data) < 3:
            return self.moving_average_predict(data)

        X = np.arange(len(data)).reshape(-1, 1)

        # 训练模型
        lr_occupancy = LinearRegression().fit(X, data['实占率'].values)
        lr_ports = LinearRegression().fit(X, data['空闲端口数'].values)
        lr_demand = LinearRegression().fit(X, data['潜在需求比'].values) if '潜在需求比' in data.columns else None

        # 预测下一个时间点
        next_time = len(data)
        pred_occupancy = lr_occupancy.predict([[next_time]])[0]
        pred_occupancy = max(0, min(1, pred_occupancy))

        pred_ports = lr_ports.predict([[next_time]])[0]
        pred_ports = max(0, safe_int(pred_ports))

        if lr_demand is not None:
            pred_demand = lr_demand.predict([[next_time]])[0]
            pred_demand = max(0, pred_demand)
        else:
            pred_demand = 0.3

        return {
            'occupancy': pred_occupancy,
            'ports': pred_ports,
            'demand': pred_demand
        }

    def calculate_terminal_growth_rate(self, data):
        """计算终端数增长率（业务发展趋势指标）"""
        if 'ftth终端数' not in data.columns or len(data) < 2:
            return 0.0

        terminals = data['ftth终端数'].dropna()
        if len(terminals) < 2:
            return 0.0

        # 计算最近几期的平均增长率
        growth_rates = []
        for i in range(1, min(len(terminals), 6)):  # 最多看最近5期的变化
            if terminals.iloc[-i-1] > 0:  # 避免除零
                rate = (terminals.iloc[-i] - terminals.iloc[-i-1]) / terminals.iloc[-i-1]
                growth_rates.append(rate)

        if growth_rates:
            return np.mean(growth_rates)
        else:
            return 0.0


class SpecializedPredictor:
    """专用预测器模块 - 从原HybridPredictor中提取，逻辑完全不变"""

    def __init__(self, change_detector, prediction_engine):
        self.change_detector = change_detector
        self.prediction_engine = prediction_engine


# ==========================================
# 核心预测器类 - 完全复制原始逻辑
# ==========================================

class HybridPredictor:
    """
    生产部署版混合预测器类
    完全保留所有预测逻辑，移除训练测试相关内容
    """

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.device_types = {}

        # 初始化模块化组件
        self.device_classifier = DeviceClassifier()
        self.trend_analyzer = TrendAnalyzer()
        self.change_detector = ChangeDetector()
        self.prediction_engine = PredictionEngine()
        self.specialized_predictor = SpecializedPredictor(self.change_detector, self.prediction_engine)

    def classify_device_type(self, data, device_id):
        """重定向到模块化组件"""
        return self.device_classifier.classify_device_type(data, device_id)

    def simple_average_predict(self, data):
        """重定向到模块化组件"""
        return self.prediction_engine.simple_average_predict(data)

    def analyze_device_trend(self, data, device_id, device_type='stable'):
        """重定向到模块化组件"""
        return self.trend_analyzer.analyze_device_trend(data, device_id, device_type)

    def moving_average_predict(self, data, window=4):
        """移动平均预测"""
        if len(data) < window:
            window = len(data)

        # 计算移动平均
        occupancy_ma = data['实占率'].rolling(window=window).mean().iloc[-1]
        ports_ma = data['空闲端口数'].rolling(window=window).mean().iloc[-1]
        demand_ma = data['潜在需求比'].rolling(window=window).mean().iloc[-1]

        return {
            'occupancy': occupancy_ma,
            'ports': int(round(ports_ma)),
            'demand': demand_ma
        }

    def linear_regression_predict(self, data):
        """重定向到模块化组件"""
        return self.prediction_engine.linear_regression_predict(data)

    def detect_sudden_change(self, data, window_size=2, threshold=0.15):
        """重定向到模块化组件"""
        return self.change_detector.detect_sudden_change(data, window_size, threshold)

    def calculate_terminal_growth_rate(self, data):
        """计算终端数增长率（业务发展趋势指标）"""
        return self.prediction_engine.calculate_terminal_growth_rate(data)

    def predict(self, data, device_id):
        """
        主预测函数：生产部署版本
        """
        # 分类设备类型
        device_type = self.classify_device_type(data, device_id)
        self.device_types[device_id] = device_type

        print(f"设备 {device_id} 分类为: {device_type}")

        # 根据重构后的两层分类策略选择预测方法
        if device_type == 'insufficient_data':
            # 数据不足设备 - 使用简单平均
            prediction = self.simple_average_predict(data)
            print(f"  警告: 数据不足，预测结果仅供参考，误差可能很大")

        elif device_type == 'stable':
            # 稳定设备 - 使用移动平均 + 趋势分析
            prediction = self.moving_average_predict(data)
            # 为stable设备添加趋势分析
            trend_analysis = self.analyze_device_trend(data, device_id, 'stable')
            print(f"  📊 趋势分析: {trend_analysis}")

        elif device_type == 'expanded':
            # 扩容设备 - 使用指数平滑预测
            prediction = self.exponential_smoothing_predict(data)

        elif device_type == 'normal':
            # 正常设备 - 使用线性回归
            prediction = self.linear_regression_predict(data)

        elif device_type == 'volatile':
            # 波动设备 - 使用线性回归（简化版本）
            prediction = self.linear_regression_predict(data)

        else:  # 其他情况，使用线性回归作为备选
            prediction = self.linear_regression_predict(data)

        return prediction

    def exponential_smoothing_predict(self, data):
        """指数平滑预测（专门用于扩容设备）"""
        if len(data) < 4:
            return self.linear_regression_predict(data)

        try:
            # 对实占率进行指数平滑
            occupancy_series = data['实占率'].values

            # 使用简单指数平滑
            from statsmodels.tsa.holtwinters import SimpleExpSmoothing

            # 实占率预测
            model_occ = SimpleExpSmoothing(occupancy_series)
            fitted_occ = model_occ.fit(smoothing_level=0.3)
            pred_occupancy = fitted_occ.forecast(1)[0]
            pred_occupancy = max(0, min(1, pred_occupancy))

            # 空闲端口数预测（考虑扩容后的容量）
            latest_capacity = data['分光器容量'].iloc[-1]
            pred_ports = int(latest_capacity * (1 - pred_occupancy))
            pred_ports = max(0, pred_ports)

            # 潜在需求比预测（使用趋势外推）
            demand_series = data['潜在需求比'].values
            if len(demand_series) >= 3:
                # 计算趋势
                x = np.arange(len(demand_series))
                slope, intercept = np.polyfit(x, demand_series, 1)
                pred_demand = slope * len(demand_series) + intercept
                pred_demand = max(0, pred_demand)
            else:
                pred_demand = demand_series[-1] if len(demand_series) > 0 else 0.5

            return {
                'occupancy': pred_occupancy,
                'ports': pred_ports,
                'demand': pred_demand
            }

        except Exception as e:
            print(f"指数平滑预测失败，使用线性回归备选方案: {e}")
            return self.linear_regression_predict(data)


# ==========================================
# 数据处理函数 - 完全复制原始逻辑
# ==========================================

def calculate_risk_score(occupancy, ports, demand, capacity):
    """计算风险评分 - 完全复制原始逻辑"""
    reasons = []

    # 实占率评分
    if occupancy >= 0.97:
        occupancy_score = 100
        reasons.append("实占率极高（≥ 97%）")
    elif occupancy >= 0.93:
        occupancy_score = 90 + (occupancy - 0.93) * 250
        reasons.append("实占率非常高（≥ 93%）")
    elif occupancy >= 0.9:
        occupancy_score = 80 + (occupancy - 0.9) * 333
        reasons.append("实占率偏高（≥ 90%）")
    elif occupancy >= 0.8:
        occupancy_score = 60 + (occupancy - 0.8) * 200
        reasons.append("实占率较高（≥ 80%）")
    else:
        occupancy_score = occupancy * 75

    # 空闲端口数评分
    if ports <= 0:
        ports_score = 100
        reasons.append("无空闲端口")
    elif ports <= 1:
        ports_score = 95
        reasons.append("空闲端口数仅剩 1 个")
    elif ports <= 2:
        ports_score = 85
        reasons.append("空闲端口数仅剩 2 个")
    elif ports <= 3:
        ports_score = 70
        reasons.append("空闲端口数仅剩 3 个")
    elif ports <= 5:
        ports_score = 50
        reasons.append("空闲端口数较少（4-5个）")
    else:
        ports_score = max(0, 50 - (ports - 5) * 5)

    # 潜在需求比评分
    if demand >= 0.8:
        demand_score = 10
        reasons.append("潜在需求比很高（≥ 80%），用户密度饱和，发展空间有限")
    elif demand >= 0.6:
        demand_score = 20
        reasons.append("潜在需求比较高（≥ 60%），用户密度较高")
    elif demand >= 0.4:
        demand_score = 30
        reasons.append("潜在需求比适中（40-60%），用户密度正常")
    elif demand >= 0.2:
        demand_score = 50
        reasons.append("潜在需求比较低（20-40%），存在用户增长空间")
    else:
        demand_score = 70
        reasons.append("潜在需求比很低（< 20%），业务发展潜力大，需关注端口需求增长")

    # 加权总分
    weights = {'occupancy': 0.5, 'ports': 0.3, 'demand': 0.2}
    total_score = (
        weights['occupancy'] * occupancy_score +
        weights['ports'] * ports_score +
        weights['demand'] * demand_score
    )

    # 特殊规则
    if occupancy >= 0.95 and ports <= 2:
        total_score = max(total_score, 85)
        reasons.append("高实占率+低空闲端口数组合风险")

    return min(100, max(0, total_score)), reasons


# ==========================================
# Flask API接口封装
# ==========================================

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局预测器实例
predictor = HybridPredictor()


def parse_device_data(data_dict):
    """
    解析前端传来的设备数据，转换为DataFrame格式

    Args:
        data_dict: 前端传来的设备数据字典

    Returns:
        pandas.DataFrame: 转换后的数据框
    """
    try:
        # 如果是历史数据列表
        if isinstance(data_dict, list):
            df = pd.DataFrame(data_dict)
        else:
            # 如果是单条数据
            df = pd.DataFrame([data_dict])

        # 确保必要的列存在
        required_columns = ['实占率', '空闲端口数', '分光器容量']
        for col in required_columns:
            if col not in df.columns:
                logger.warning(f"缺少必要列: {col}")
                # 设置默认值
                if col == '实占率':
                    df[col] = 0.5
                elif col == '空闲端口数':
                    df[col] = 24
                elif col == '分光器容量':
                    df[col] = 48

        # 确保可选列存在
        optional_columns = {
            '潜在需求比': 0.3,
            'ftth终端数': 100,
            '覆盖的工程级的线路到达房间数': 300,
            '容量变化': 0
        }

        for col, default_value in optional_columns.items():
            if col not in df.columns:
                df[col] = default_value

        return df

    except Exception as e:
        logger.error(f"解析设备数据失败: {e}")
        raise ValueError(f"数据格式错误: {e}")


@app.route('/api/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    用于检查API服务是否正常运行

    Returns:
        JSON: 服务状态信息
    """
    return jsonify({
        'status': 'healthy',
        'service': 'splitter-prediction-api',
        'version': '1.0.0',
        'tensorflow_available': TENSORFLOW_AVAILABLE,
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/predict/single', methods=['POST'])
def predict_single_device():
    """
    单设备预测接口
    对应后端 SplitterService.predictSingleDevice() 方法

    请求格式:
    {
        "deviceCode": "设备编码",
        "period": "预测周期(week/month)",
        "historicalData": [
            {
                "实占率": 0.85,
                "空闲端口数": 8,
                "分光器容量": 48,
                "潜在需求比": 0.6,
                "ftth终端数": 120,
                "覆盖的工程级的线路到达房间数": 200
            }
            // ... 更多历史数据
        ]
    }

    返回格式:
    {
        "hasData": true,
        "设备编码": "设备编码",
        "prediction": {
            "nextWeekRate": 0.87,
            "nextMonthRate": 0.89,
            "trend": "increasing",
            "confidence": 85.0
        },
        "recommendations": {
            "expansionAdvice": "建议扩容方案",
            "adjustmentAdvice": "调整建议",
            "priority": "high/medium/low"
        },
        "historicalData": [...],
        "deviceType": "设备类型",
        "trendAnalysis": "趋势分析文本"
    }
    """
    try:
        # 获取请求数据
        request_data = request.get_json()

        if not request_data:
            return jsonify({'error': '请求数据为空'}), 400

        device_code = request_data.get('deviceCode')
        period = request_data.get('period', 'week')
        historical_data = request_data.get('historicalData', [])

        if not device_code:
            return jsonify({'error': '缺少设备编码'}), 400

        if not historical_data:
            return jsonify({'error': '缺少历史数据'}), 400

        # 解析历史数据
        data_df = parse_device_data(historical_data)

        if len(data_df) == 0:
            return jsonify({
                'hasData': False,
                'message': '历史数据为空，无法进行预测'
            })

        # 执行预测
        prediction_result = predictor.predict(data_df, device_code)

        # 获取设备类型
        device_type = predictor.device_types.get(device_code, 'unknown')

        # 生成趋势分析
        trend_analysis = predictor.analyze_device_trend(data_df, device_code, device_type)

        # 计算预测值
        current_occupancy = data_df['实占率'].iloc[-1]

        # 简单的趋势计算
        if len(data_df) >= 2:
            recent_trend = data_df['实占率'].iloc[-1] - data_df['实占率'].iloc[-2]
        else:
            recent_trend = 0

        # 预测下周和下月的实占率
        next_week_rate = min(1.0, max(0.0, prediction_result['occupancy'] + recent_trend * 0.5))
        next_month_rate = min(1.0, max(0.0, prediction_result['occupancy'] + recent_trend * 2))

        # 判断趋势
        if recent_trend > 0.02:
            trend = "increasing"
        elif recent_trend < -0.02:
            trend = "decreasing"
        else:
            trend = "stable"

        # 生成建议
        recommendations = generate_recommendations(next_month_rate, trend)

        # 构造返回结果
        result = {
            'hasData': True,
            '设备编码': device_code,
            'prediction': {
                'nextWeekRate': round(next_week_rate * 100, 2),  # 转换为百分比
                'nextMonthRate': round(next_month_rate * 100, 2),
                'trend': trend,
                'confidence': 85.0
            },
            'recommendations': recommendations,
            'historicalData': historical_data,
            'deviceType': device_type,
            'trendAnalysis': trend_analysis
        }

        logger.info(f"单设备预测完成: {device_code}, 类型: {device_type}")
        return jsonify(result)

    except Exception as e:
        logger.error(f"单设备预测失败: {e}")
        return jsonify({
            'hasData': False,
            'error': f'预测失败: {str(e)}'
        }), 500


def generate_recommendations(predicted_rate, trend):
    """
    生成扩容和调整建议

    Args:
        predicted_rate: 预测实占率 (0-1)
        trend: 趋势 (increasing/decreasing/stable)

    Returns:
        dict: 建议方案
    """
    recommendations = {}

    if predicted_rate >= 0.95:
        recommendations['expansionAdvice'] = "紧急扩容：建议立即增加分光器容量"
        recommendations['adjustmentAdvice'] = "立即进行负载均衡调整"
        recommendations['priority'] = "high"
    elif predicted_rate >= 0.85:
        recommendations['expansionAdvice'] = "建议在2周内增加分光器容量"
        recommendations['adjustmentAdvice'] = "可考虑将部分用户迁移至邻近设备"
        recommendations['priority'] = "medium"
    elif trend == "increasing":
        recommendations['expansionAdvice'] = "建议关注设备使用情况，适时扩容"
        recommendations['adjustmentAdvice'] = "定期监控设备负载情况"
        recommendations['priority'] = "low"
    else:
        recommendations['expansionAdvice'] = "当前容量充足，无需扩容"
        recommendations['adjustmentAdvice'] = "保持现有配置"
        recommendations['priority'] = "low"

    return recommendations


@app.route('/api/predict/batch', methods=['POST'])
def predict_batch_devices():
    """
    批量设备预测接口
    对应后端 SplitterService.predictAllDevices() 方法

    请求格式:
    {
        "devices": [
            {
                "deviceCode": "设备编码1",
                "historicalData": [...]
            },
            {
                "deviceCode": "设备编码2",
                "historicalData": [...]
            }
        ]
    }

    返回格式:
    [
        {
            "设备编码": "设备编码1",
            "预测实占率": 87.5,
            "预测空闲数": 6,
            "预测状态": "attention",
            "deviceType": "stable"
        }
    ]
    """
    try:
        request_data = request.get_json()

        if not request_data or 'devices' not in request_data:
            return jsonify({'error': '请求数据格式错误'}), 400

        devices = request_data['devices']
        predictions = []

        for device_info in devices:
            device_code = device_info.get('deviceCode')
            historical_data = device_info.get('historicalData', [])

            if not device_code or not historical_data:
                continue

            try:
                # 解析数据
                data_df = parse_device_data(historical_data)

                # 执行预测
                prediction_result = predictor.predict(data_df, device_code)
                device_type = predictor.device_types.get(device_code, 'unknown')

                # 计算预测值
                predicted_rate = prediction_result['occupancy'] * 100  # 转换为百分比
                predicted_free_count = prediction_result['ports']

                # 计算预测状态
                if predicted_rate >= 90:
                    predicted_status = "alarm"
                elif predicted_rate >= 80:
                    predicted_status = "attention"
                else:
                    predicted_status = "normal"

                result = {
                    '设备编码': device_code,
                    '预测实占率': round(predicted_rate, 2),
                    '预测空闲数': predicted_free_count,
                    '预测状态': predicted_status,
                    'deviceType': device_type
                }

                predictions.append(result)

            except Exception as e:
                logger.error(f"设备 {device_code} 预测失败: {e}")
                continue

        logger.info(f"批量预测完成，成功预测设备数量: {len(predictions)}")
        return jsonify(predictions)

    except Exception as e:
        logger.error(f"批量预测失败: {e}")
        return jsonify({'error': f'批量预测失败: {str(e)}'}), 500


@app.route('/api/analyze/trend', methods=['POST'])
def analyze_trend():
    """
    趋势分析接口
    对应后端趋势数据分析功能

    请求格式:
    {
        "deviceCode": "设备编码",
        "timeRange": "时间范围",
        "historicalData": [...]
    }

    返回格式:
    {
        "deviceCode": "设备编码",
        "trendAnalysis": "趋势分析文本",
        "deviceType": "设备类型",
        "trendData": [
            {"week": "第1周", "occupancyRate": 85.2},
            {"week": "第2周", "occupancyRate": 86.1}
        ]
    }
    """
    try:
        request_data = request.get_json()

        device_code = request_data.get('deviceCode')
        time_range = request_data.get('timeRange', 'recent')
        historical_data = request_data.get('historicalData', [])

        if not device_code or not historical_data:
            return jsonify({'error': '缺少必要参数'}), 400

        # 解析数据
        data_df = parse_device_data(historical_data)

        # 分类设备类型
        device_type = predictor.classify_device_type(data_df, device_code)

        # 生成趋势分析
        trend_analysis = predictor.analyze_device_trend(data_df, device_code, device_type)

        # 构造趋势数据
        trend_data = []
        for i, row in data_df.iterrows():
            trend_data.append({
                'week': f'第{i+1}周',
                'occupancyRate': round(row['实占率'] * 100, 2)
            })

        result = {
            'deviceCode': device_code,
            'trendAnalysis': trend_analysis,
            'deviceType': device_type,
            'trendData': trend_data
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"趋势分析失败: {e}")
        return jsonify({'error': f'趋势分析失败: {str(e)}'}), 500


@app.route('/api/analyze/risk', methods=['POST'])
def analyze_risk():
    """
    风险评估接口
    对应后端风险评估功能

    请求格式:
    {
        "deviceCode": "设备编码",
        "currentData": {
            "实占率": 0.85,
            "空闲端口数": 8,
            "分光器容量": 48,
            "潜在需求比": 0.6
        }
    }

    返回格式:
    {
        "deviceCode": "设备编码",
        "riskScore": 75,
        "riskLevel": "medium",
        "reasons": ["实占率较高", "空闲端口数较少"],
        "recommendations": "风险缓解建议"
    }
    """
    try:
        request_data = request.get_json()

        device_code = request_data.get('deviceCode')
        current_data = request_data.get('currentData', {})

        if not device_code or not current_data:
            return jsonify({'error': '缺少必要参数'}), 400

        # 提取数据
        occupancy = current_data.get('实占率', 0.5)
        ports = current_data.get('空闲端口数', 24)
        demand = current_data.get('潜在需求比', 0.3)
        capacity = current_data.get('分光器容量', 48)

        # 计算风险评分
        risk_score, reasons = calculate_risk_score(occupancy, ports, demand, capacity)

        # 确定风险等级
        if risk_score >= 80:
            risk_level = "high"
        elif risk_score >= 50:
            risk_level = "medium"
        else:
            risk_level = "low"

        # 生成建议
        if risk_level == "high":
            recommendations = "建议立即采取扩容措施，密切监控设备状态"
        elif risk_level == "medium":
            recommendations = "建议制定扩容计划，定期检查设备负载"
        else:
            recommendations = "当前风险较低，保持正常监控即可"

        result = {
            'deviceCode': device_code,
            'riskScore': round(risk_score, 2),
            'riskLevel': risk_level,
            'reasons': reasons,
            'recommendations': recommendations
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"风险评估失败: {e}")
        return jsonify({'error': f'风险评估失败: {str(e)}'}), 500


@app.route('/api/config', methods=['GET'])
def get_config():
    """
    获取预测配置接口
    返回预测算法的配置参数

    返回格式:
    {
        "healthThresholds": {
            "attention": 80,
            "alarm": 90
        },
        "predictionConfig": {
            "algorithm": "hybrid",
            "confidence": 85
        },
        "supportedDeviceTypes": ["stable", "expanded", "normal", "volatile", "insufficient_data"]
    }
    """
    config = {
        'healthThresholds': {
            'attention': 80,
            'alarm': 90
        },
        'predictionConfig': {
            'algorithm': 'hybrid',
            'confidence': 85
        },
        'supportedDeviceTypes': ['stable', 'expanded', 'normal', 'volatile', 'insufficient_data'],
        'apiVersion': '1.0.0',
        'tensorflowAvailable': TENSORFLOW_AVAILABLE
    }

    return jsonify(config)


if __name__ == '__main__':
    """
    启动Flask应用

    使用方法:
    python splitter_prediction_api.py

    API将在 http://localhost:5000 启动
    """
    logger.info("启动分光器预测API服务...")
    logger.info(f"TensorFlow可用性: {TENSORFLOW_AVAILABLE}")

    # 开发环境配置
    app.run(
        host='0.0.0.0',  # 允许外部访问
        port=5000,       # 端口号
        debug=True,      # 开启调试模式
        threaded=True    # 支持多线程
    )
