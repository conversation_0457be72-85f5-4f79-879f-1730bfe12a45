{"cells": [{"cell_type": "markdown", "id": "ae0995f3-4a80-4feb-927b-4077b4ece66b", "metadata": {}, "source": ["## 练习题目：让AI生成工作周报\n", "\n", "**输入**：工作列表（工作清单）\n", "\n", "**背景信息**：个人信息。\n", "\n", "**输出**：工作周报"]}, {"cell_type": "code", "execution_count": 1, "id": "940985a9-4eda-4376-9663-dc9024afa21b", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "url_ds = \"https://api.deepseek.com\"\n", "deepseek_key=os.environ[\"DS_API_KEY\"]\n", "\n", "chat = ChatOpenAI(model=\"deepseek-chat\", # V3: deepseek-chat;  R1: deepseek-reason\n", "                    base_url=url_ds,\n", "                    api_key=deepseek_key)"]}, {"cell_type": "code", "execution_count": 4, "id": "d6631edc-d4d8-46b9-af56-7859ff63ca54", "metadata": {}, "outputs": [], "source": ["#step2 创建promopt的模板\n", "from langchain.prompts import ChatPromptTemplate\n", "template_str='''你是一个周报生成的小助手，你的任务是根据工作列表，生成一份工作周报。\n", "背景信息：我是张老师，在华为培训中心任职。\n", "本周时间：2025年7月14日~7月18日\n", "工作列表：{work_list}'''\n", "prompt_template=ChatPromptTemplate.from_template(template_str)"]}, {"cell_type": "code", "execution_count": 5, "id": "da990b52-12b2-400c-b1f0-a9887d7dac81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**华为培训中心工作周报**  \n", "**姓名**：张老师  \n", "**日期**：2025年7月14日~7月18日  \n", "\n", "---\n", "\n", "### **一、本周工作内容总结**  \n", "1. **周一（7月14日）**  \n", "   - 参加AI培训课程，学习最新人工智能技术及行业应用案例，为后续课程开发储备知识。  \n", "\n", "2. **周二（7月15日）**  \n", "   - 拜访客户A，沟通培训需求及反馈，针对客户提出的定制化课程要求进行初步方案设计。  \n", "\n", "3. **周三（7月16日）**  \n", "   - 完成培训中心教学设备的例行维护，确保设备运行正常，保障后续课程顺利开展。  \n", "\n", "4. **周四（7月17日）**  \n", "   - 参加AI认证考试（如华为AI认证或其他指定考试），巩固专业知识并获取行业资质认证。  \n", "\n", "5. **周五（7月18日）**  \n", "   - 未列具体事项，可能用于整理本周工作资料或准备下周计划。  \n", "\n", "---\n", "\n", "### **二、本周工作成果**  \n", "- 完成AI技术知识更新，客户需求调研及设备维护等基础工作。  \n", "- 通过AI认证考试，提升个人专业能力，为团队技术储备提供支持。  \n", "\n", "---\n", "\n", "### **三、下周工作计划**  \n", "1. 根据客户A的需求细化培训方案，协调资源推进落地。  \n", "2. 结合AI培训内容，优化现有课程设计。  \n", "3. 检查维护后设备的使用情况，确保稳定性。  \n", "\n", "---\n", "\n", "**备注**：如需调整工作重点或补充细节，请随时沟通。  \n", "\n", "---  \n", "**生成时间**：2025年7月18日  \n", "\n", "（注：周五未列事项可根据实际情况补充说明。）\n"]}], "source": ["#step 生成prompt， 启动LLM\n", "customer_messages=prompt_template.format_messages(work_list='1.周一参考AI培训。2.周二拜访客户A。3.周三。设备的维护。4。参加AI的认证考试')\n", "customer_response=chat.invoke(customer_messages)\n", "print(customer_response.content)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9857a714-a552-4c74-9f6b-94847212901c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}