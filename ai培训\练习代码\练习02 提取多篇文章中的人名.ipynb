{"cells": [{"cell_type": "markdown", "id": "7e914cf4-0387-4b06-82ef-3bb4f72f6b9b", "metadata": {}, "source": ["## 练习02：提取多篇文章中的人名\n", "\n", "输入：多篇txt文件\n", "\n", "输出：每篇文章对应的人名，文章的标题。"]}, {"cell_type": "code", "execution_count": 1, "id": "0483334b-aabf-4a53-9368-ef5cea16debc", "metadata": {}, "outputs": [], "source": ["#创建和大模型的连接器\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "url_ds = \"https://api.deepseek.com\"\n", "deepseek_key=os.environ[\"DS_API_KEY\"]\n", "\n", "chat = ChatOpenAI(model=\"deepseek-chat\", # V3: deepseek-chat;  R1: deepseek-reason\n", "                    base_url=url_ds,\n", "                    api_key=deepseek_key)"]}, {"cell_type": "code", "execution_count": 2, "id": "30f24733-b0fd-4e3f-9c3c-4fdbe30861bd", "metadata": {}, "outputs": [], "source": ["#创建prompt模板\n", "from langchain.prompts import ChatPromptTemplate\n", "template_str='''你是一个文章关键信息提取的小助手，你的任务是：\n", "1.根据提取文章中的人名（注意：不需要记者、编辑的姓名）。\n", "2.生成文章的标题\n", "输出格式：\n", "文章的标题~人名列表\n", "\n", "参考文章：{article}'''\n", "prompt_template=ChatPromptTemplate.from_template(template_str)"]}, {"cell_type": "code", "execution_count": 3, "id": "866e43d9-6f05-4e5d-853d-ee42378a3c54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文章的标题~人名列表  \n", "娃哈哈百亿遗产争夺案：非婚生子女诉讼与杜建英角色曝光~宗继昌（<PERSON><PERSON>）、宗婕莉（<PERSON>）、宗继盛（<PERSON>）、宗馥莉、杜建英、宗蕊\n", "昆明理工大学与云南省工投集团开展考察交流与访企拓岗活动~王华,王国栋,李钰,徐俊祥,牛昱宇\n", "王灿否认参加名媛培训班~杜淳、王灿、黄晓明、叶珂\n"]}], "source": ["#扫描news目录，把所有的txt的文件，读出来，生成prompt，发给大模型\n", "import os\n", "path='D:\\\\我的培训\\\\250718 南京电信\\\\news'\n", "for file_name in os.listdir(path):\n", "    if file_name[-3:]=='txt':\n", "        f=open(path+'\\\\'+file_name,'r',encoding='utf-8')\n", "        article=f.read()\n", "        f.close()\n", "        #生成prompt\n", "        customer_messages=prompt_template.format_messages(article=article)\n", "        #调用大模型\n", "        customer_response=chat.invoke(customer_messages)\n", "        print(customer_response.content)"]}, {"cell_type": "code", "execution_count": 4, "id": "e08b8e0d-36c2-4fe0-bc5f-937f979830b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["根据提供的文章内容，以下是提取的关键信息并按照指定格式列出的知识图谱节点和边：\n", "\n", "1. (宗庆后，父女关系，宗馥莉)\n", "2. (宗庆后，父子关系，宗继昌)\n", "3. (宗庆后，父女关系，宗婕莉)\n", "4. (宗庆后，父子关系，宗继盛)\n", "5. (宗庆后，兄妹关系，宗蕊)\n", "6. (宗庆后，非婚伴侣关系，杜建英)\n", "7. (杜建英，母子关系，宗继昌)\n", "8. (杜建英，母女关系，宗婕莉)\n", "9. (杜建英，母子关系，宗继盛)\n", "10. (宗馥莉，堂兄妹关系，宗继昌)\n", "11. (宗馥莉，堂兄妹关系，宗婕莉)\n", "12. (宗馥莉，堂兄妹关系，宗继盛)\n", "13. (杜建英，前同事关系，宗庆后) [注：杜建英曾是娃哈哈高管]\n", "14. (宗蕊，股东关系，杜建英) [注：宗蕊是杜建英公司的股东]\n", "15. (宗馥莉，控制关系，宏胜系) [注：宗馥莉实际控制宏胜系企业]\n", "\n", "注：\n", "1. 记者、编辑姓名已按需排除\n", "2. 部分关系（如非婚伴侣、堂兄妹）是根据上下文合理推断得出\n", "3. 公司实体（如宏胜系）未作为节点列出，仅作为关系说明\n", "4. 法律人士陈焘因属于专业意见提供者而非事件核心人物，故未列入\n", "根据提供的文章内容，以下是提取的人物及其关系：\n", "\n", "1. (王华，同事关系，牛昱宇)\n", "2. (王国栋，同事关系，李钰)\n", "3. (王国栋，同事关系，徐俊祥)\n", "4. (王华，合作关系，王国栋)\n", "根据提供的文章，以下是提取的知识图谱信息：\n", "\n", "1. 节点与边：\n", "- (杜淳, 夫妻关系, 王灿)\n", "- (黄晓明, 恋情关系, 叶珂)\n", "- (王灿, 同框合照关系, 叶珂) （注：根据“同框合照”可推断存在社交关联）\n", "\n", "2. 补充说明：\n", "- 黄晓明与叶珂的恋情为文章明确提及的公开事件。\n", "- 王灿与叶珂的关系通过“同框合照”及网友质疑间接建立，虽未明确具体关系，但可作为临时关联边保留。\n", "- 杜淳与王灿的夫妻关系有公开婚讯及综艺合作佐证。\n", "\n", "输出列表：\n", "[  \n", "  (\"杜淳\", \"夫妻关系\", \"王灿\"),  \n", "  (\"黄晓明\", \"恋情关系\", \"叶珂\"),  \n", "  (\"王灿\", \"同框合照关系\", \"叶珂\")  \n", "]\n"]}], "source": ["#更新模板：知识图谱的提取\n", "from langchain.prompts import ChatPromptTemplate\n", "template_str='''你是一个文章关键信息提取的小助手，我希望创建知识图谱。你的任务是：\n", "1.根据提取文章中的人名（注意：不需要记者、编辑的姓名）作为知识图谱的节点。\n", "2.提取人与人之间的关系（可以推理出关系）。作为知识图谱的边。\n", "输出格式（列表）：\n", "(张三，同事关系，李四)\n", "\n", "参考文章：{article}'''\n", "prompt_template=ChatPromptTemplate.from_template(template_str)\n", "#分步提取\n", "path='D:\\\\我的培训\\\\250718 南京电信\\\\news'\n", "for file_name in os.listdir(path):\n", "    if file_name[-3:]=='txt':\n", "        f=open(path+'\\\\'+file_name,'r',encoding='utf-8')\n", "        article=f.read()\n", "        f.close()\n", "        #生成prompt\n", "        customer_messages=prompt_template.format_messages(article=article)\n", "        #调用大模型\n", "        customer_response=chat.invoke(customer_messages)\n", "        print(customer_response.content)"]}, {"cell_type": "code", "execution_count": 9, "id": "1780970e-6640-4d4e-8526-fef39a634aa0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"person1\": string  // 人的姓名\n", "\t\"relationship\": string  // person1和person2的关系描述\n", "\t\"person2\": string  // 人的姓名\n", "}\n", "```\n"]}], "source": ["#能否以json的格式输出\n", "from langchain.output_parsers import ResponseSchema\n", "from langchain.output_parsers import StructuredOutputParser\n", "person_schema=ResponseSchema(name='person1',description='人的姓名')\n", "relationship_schema=ResponseSchema(name='relationship',description='person1和person2的关系描述')\n", "person2_schema=ResponseSchema(name='person2',description='人的姓名')\n", "output_parser=StructuredOutputParser.from_response_schemas([person_schema,relationship_schema,person2_schema])\n", "format_instructions=output_parser.get_format_instructions()\n", "print(format_instructions)"]}, {"cell_type": "code", "execution_count": 14, "id": "3784b096-ab0b-418d-9b19-0bbb6a0b89f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'person1': '宗馥莉', 'relationship': '父女关系', 'person2': '宗庆后'}, {'person1': '宗继昌', 'relationship': '父子关系', 'person2': '宗庆后'}, {'person1': '宗婕莉', 'relationship': '父女关系', 'person2': '宗庆后'}, {'person1': '宗继盛', 'relationship': '父子关系', 'person2': '宗庆后'}, {'person1': '杜建英', 'relationship': '非婚生子女生母', 'person2': '宗继昌'}, {'person1': '杜建英', 'relationship': '非婚生子女生母', 'person2': '宗婕莉'}, {'person1': '杜建英', 'relationship': '非婚生子女生母', 'person2': '宗继盛'}, {'person1': '杜建英', 'relationship': '前同事关系', 'person2': '宗庆后'}, {'person1': '宗蕊', 'relationship': '兄妹关系', 'person2': '宗庆后'}, {'person1': '宗蕊', 'relationship': '股东关系', 'person2': '杜建英'}]\n", "[{'person1': '王华', 'relationship': '校长', 'person2': '昆明理工大学'}, {'person1': '王华', 'relationship': '访企拓岗活动参与者', 'person2': '云南省工投集团'}, {'person1': '王国栋', 'relationship': '董事长', 'person2': '云南省工投集团'}, {'person1': '李钰', 'relationship': '副总裁', 'person2': '云南省工投集团'}, {'person1': '徐俊祥', 'relationship': '副总裁', 'person2': '云南省工投集团'}, {'person1': '牛昱宇', 'relationship': '副校长', 'person2': '昆明理工大学'}, {'person1': '王华', 'relationship': '合作意向', 'person2': '王国栋'}, {'person1': '昆明理工大学', 'relationship': '校企合作', 'person2': '云南省工投集团'}]\n", "[{'person1': '王灿', 'relationship': '夫妻关系', 'person2': '杜淳'}, {'person1': '黄晓明', 'relationship': '恋人关系', 'person2': '叶珂'}]\n"]}], "source": ["from langchain.prompts import ChatPromptTemplate\n", "import json\n", "template_str='''你是一个文章关键信息提取的小助手，我希望创建知识图谱。你的任务是：\n", "1.根据提取文章中的人名（注意：不需要记者、编辑的姓名）作为知识图谱的节点。\n", "2.提取人与人之间的关系（可以推理出关系）。作为知识图谱的边。\n", "输出格式Json，输出的示例：\n", "(person1，同事关系，person2)\n", "\n", "参考文章：{article}\n", "\n", "{format_instructions}'''\n", "\n", "prompt_template=ChatPromptTemplate.from_template(template_str)\n", "#分步提取\n", "path='D:\\\\我的培训\\\\250718 南京电信\\\\news'\n", "for file_name in os.listdir(path):\n", "    if file_name[-3:]=='txt':\n", "        f=open(path+'\\\\'+file_name,'r',encoding='utf-8')\n", "        article=f.read()\n", "        f.close()\n", "        #生成prompt\n", "        customer_messages=prompt_template.format_messages(article=article,format_instructions=format_instructions)\n", "        #调用大模型\n", "        customer_response=chat.invoke(customer_messages)\n", "        #output_dict=output_parser.parse(customer_response.content) #\n", "        output_dict=json.loads(customer_response.content.replace('```json','').replace('```',''))\n", "        print(output_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "7fdbc235-882e-4748-86f0-78a3fc35b50c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}